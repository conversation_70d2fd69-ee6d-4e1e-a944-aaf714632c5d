------------- 09-16-29-20-186544 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 1.000000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001000 -> λ_new=0.001400
Lambda Change: Δλ = 0.000400
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=895, accuracy=1.0
  -> Calculation: 1.0 - 0.001400 * 895 = -0.253000
Completion 1: length=902, accuracy=1.0
  -> Calculation: 1.0 - 0.001400 * 902 = -0.262800
Completion 2: length=682, accuracy=1.0
  -> Calculation: 1.0 - 0.001400 * 682 = 0.045200
Completion 3: length=948, accuracy=1.0
  -> Calculation: 1.0 - 0.001400 * 948 = -0.327200
Completion 4: length=685, accuracy=1.0
  -> Calculation: 1.0 - 0.001400 * 685 = 0.041000
Completion 5: length=816, accuracy=1.0
  -> Calculation: 1.0 - 0.001400 * 816 = -0.142400
Completion 6: length=672, accuracy=1.0
  -> Calculation: 1.0 - 0.001400 * 672 = 0.059200
Completion 7: length=861, accuracy=1.0
  -> Calculation: 1.0 - 0.001400 * 861 = -0.205400
------------- 09-16-29-20-187358 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 0.250000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001000 -> λ_new=0.000650
Lambda Change: Δλ = -0.000350
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=657, accuracy=0.0
  -> Calculation: 0.0 - 0.000650 * 657 = -0.427050
Completion 1: length=686, accuracy=0.0
  -> Calculation: 0.0 - 0.000650 * 686 = -0.445900
Completion 2: length=860, accuracy=1.0
  -> Calculation: 1.0 - 0.000650 * 860 = 0.441000
Completion 3: length=1067, accuracy=1.0
  -> Calculation: 1.0 - 0.000650 * 1067 = 0.306450
Completion 4: length=709, accuracy=0.0
  -> Calculation: 0.0 - 0.000650 * 709 = -0.460850
Completion 5: length=763, accuracy=0.0
  -> Calculation: 0.0 - 0.000650 * 763 = -0.495950
Completion 6: length=679, accuracy=0.0
  -> Calculation: 0.0 - 0.000650 * 679 = -0.441350
Completion 7: length=957, accuracy=0.0
  -> Calculation: 0.0 - 0.000650 * 957 = -0.622050
------------- 09-16-29-20-188316 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.375000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001000 -> λ_new=0.000775
Lambda Change: Δλ = -0.000225
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=671, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 671 = 0.479975
Completion 1: length=500, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 500 = -0.387500
Completion 2: length=736, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 736 = 0.429600
Completion 3: length=903, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 903 = -0.699825
Completion 4: length=706, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 706 = -0.547150
Completion 5: length=1166, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 1166 = -0.903650
Completion 6: length=1063, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 1063 = -0.823825
Completion 7: length=630, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 630 = 0.511750
------------- 09-16-29-20-191444 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 0.375000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001000 -> λ_new=0.000775
Lambda Change: Δλ = -0.000225
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=821, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 821 = 0.363725
Completion 1: length=579, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 579 = 0.551275
Completion 2: length=668, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 668 = -0.517700
Completion 3: length=1782, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 1782 = -1.381050
Completion 4: length=691, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 691 = -0.535525
Completion 5: length=653, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 653 = -0.506075
Completion 6: length=1855, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 1855 = -0.437625
Completion 7: length=642, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 642 = -0.497550
------------- 09-16-29-41-258209 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 1.000000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000775 -> λ_new=0.001175
Lambda Change: Δλ = 0.000400
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=889, accuracy=1.0
  -> Calculation: 1.0 - 0.001175 * 889 = -0.044575
Completion 1: length=620, accuracy=1.0
  -> Calculation: 1.0 - 0.001175 * 620 = 0.271500
Completion 2: length=877, accuracy=1.0
  -> Calculation: 1.0 - 0.001175 * 877 = -0.030475
Completion 3: length=836, accuracy=1.0
  -> Calculation: 1.0 - 0.001175 * 836 = 0.017700
Completion 4: length=526, accuracy=1.0
  -> Calculation: 1.0 - 0.001175 * 526 = 0.381950
Completion 5: length=793, accuracy=1.0
  -> Calculation: 1.0 - 0.001175 * 793 = 0.068225
Completion 6: length=763, accuracy=1.0
  -> Calculation: 1.0 - 0.001175 * 763 = 0.103475
Completion 7: length=583, accuracy=1.0
  -> Calculation: 1.0 - 0.001175 * 583 = 0.314975
------------- 09-16-29-41-294353 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.625000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000775 -> λ_new=0.000800
Lambda Change: Δλ = 0.000025
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=1307, accuracy=1.0
  -> Calculation: 1.0 - 0.000800 * 1307 = -0.045600
Completion 1: length=469, accuracy=0.0
  -> Calculation: 0.0 - 0.000800 * 469 = -0.375200
Completion 2: length=687, accuracy=0.0
  -> Calculation: 0.0 - 0.000800 * 687 = -0.549600
Completion 3: length=523, accuracy=1.0
  -> Calculation: 1.0 - 0.000800 * 523 = 0.581600
Completion 4: length=1277, accuracy=1.0
  -> Calculation: 1.0 - 0.000800 * 1277 = -0.021600
Completion 5: length=1299, accuracy=1.0
  -> Calculation: 1.0 - 0.000800 * 1299 = -0.039200
Completion 6: length=1166, accuracy=1.0
  -> Calculation: 1.0 - 0.000800 * 1166 = 0.067200
Completion 7: length=580, accuracy=0.0
  -> Calculation: 0.0 - 0.000800 * 580 = -0.464000
------------- 09-16-29-41-323077 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 0.750000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001400 -> λ_new=0.001550
Lambda Change: Δλ = 0.000150
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=1681, accuracy=1.0
  -> Calculation: 1.0 - 0.001550 * 1681 = -1.605550
Completion 1: length=997, accuracy=1.0
  -> Calculation: 1.0 - 0.001550 * 997 = -0.545350
Completion 2: length=1337, accuracy=0.0
  -> Calculation: 0.0 - 0.001550 * 1337 = -2.072350
Completion 3: length=491, accuracy=1.0
  -> Calculation: 1.0 - 0.001550 * 491 = 0.238950
Completion 4: length=838, accuracy=0.0
  -> Calculation: 0.0 - 0.001550 * 838 = -1.298900
Completion 5: length=1190, accuracy=1.0
  -> Calculation: 1.0 - 0.001550 * 1190 = -0.844500
Completion 6: length=1706, accuracy=1.0
  -> Calculation: 1.0 - 0.001550 * 1706 = -1.644300
Completion 7: length=522, accuracy=1.0
  -> Calculation: 1.0 - 0.001550 * 522 = 0.190900
------------- 09-16-29-41-351742 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 0.625000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000650 -> λ_new=0.000675
Lambda Change: Δλ = 0.000025
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=1016, accuracy=1.0
  -> Calculation: 1.0 - 0.000675 * 1016 = 0.314200
Completion 1: length=1302, accuracy=0.0
  -> Calculation: 0.0 - 0.000675 * 1302 = -0.878850
Completion 2: length=610, accuracy=0.0
  -> Calculation: 0.0 - 0.000675 * 610 = -0.411750
Completion 3: length=955, accuracy=1.0
  -> Calculation: 1.0 - 0.000675 * 955 = 0.355375
Completion 4: length=1529, accuracy=1.0
  -> Calculation: 1.0 - 0.000675 * 1529 = -0.032075
Completion 5: length=510, accuracy=1.0
  -> Calculation: 1.0 - 0.000675 * 510 = 0.655750
Completion 6: length=1116, accuracy=1.0
  -> Calculation: 1.0 - 0.000675 * 1116 = 0.246700
Completion 7: length=655, accuracy=0.0
  -> Calculation: 0.0 - 0.000675 * 655 = -0.442125
------------- 09-16-29-54-984945 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 0.625000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001550 -> λ_new=0.001575
Lambda Change: Δλ = 0.000025
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=776, accuracy=1.0
  -> Calculation: 1.0 - 0.001575 * 776 = -0.222200
Completion 1: length=1053, accuracy=1.0
  -> Calculation: 1.0 - 0.001575 * 1053 = -0.658475
Completion 2: length=674, accuracy=1.0
  -> Calculation: 1.0 - 0.001575 * 674 = -0.061550
Completion 3: length=458, accuracy=1.0
  -> Calculation: 1.0 - 0.001575 * 458 = 0.278650
Completion 4: length=905, accuracy=0.0
  -> Calculation: 0.0 - 0.001575 * 905 = -1.425375
Completion 5: length=831, accuracy=1.0
  -> Calculation: 1.0 - 0.001575 * 831 = -0.308825
Completion 6: length=465, accuracy=0.0
  -> Calculation: 0.0 - 0.001575 * 465 = -0.732375
Completion 7: length=666, accuracy=0.0
  -> Calculation: 0.0 - 0.001575 * 666 = -1.048950
------------- 09-16-29-54-985007 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.250000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000675 -> λ_new=0.000325
Lambda Change: Δλ = -0.000350
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=573, accuracy=0.0
  -> Calculation: 0.0 - 0.000325 * 573 = -0.186225
Completion 1: length=876, accuracy=1.0
  -> Calculation: 1.0 - 0.000325 * 876 = 0.715300
Completion 2: length=792, accuracy=1.0
  -> Calculation: 1.0 - 0.000325 * 792 = 0.742600
Completion 3: length=765, accuracy=0.0
  -> Calculation: 0.0 - 0.000325 * 765 = -0.248625
Completion 4: length=712, accuracy=0.0
  -> Calculation: 0.0 - 0.000325 * 712 = -0.231400
Completion 5: length=656, accuracy=0.0
  -> Calculation: 0.0 - 0.000325 * 656 = -0.213200
Completion 6: length=555, accuracy=0.0
  -> Calculation: 0.0 - 0.000325 * 555 = -0.180375
Completion 7: length=1085, accuracy=0.0
  -> Calculation: 0.0 - 0.000325 * 1085 = -0.352625
------------- 09-16-29-54-991646 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.000000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001175 -> λ_new=0.000575
Lambda Change: Δλ = -0.000600
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=806, accuracy=0.0
  -> Calculation: 0.0 - 0.000575 * 806 = -0.463450
Completion 1: length=835, accuracy=0.0
  -> Calculation: 0.0 - 0.000575 * 835 = -0.480125
Completion 2: length=730, accuracy=0.0
  -> Calculation: 0.0 - 0.000575 * 730 = -0.419750
Completion 3: length=1055, accuracy=0.0
  -> Calculation: 0.0 - 0.000575 * 1055 = -0.606625
Completion 4: length=643, accuracy=0.0
  -> Calculation: 0.0 - 0.000575 * 643 = -0.369725
Completion 5: length=995, accuracy=0.0
  -> Calculation: 0.0 - 0.000575 * 995 = -0.572125
Completion 6: length=455, accuracy=0.0
  -> Calculation: 0.0 - 0.000575 * 455 = -0.261625
Completion 7: length=909, accuracy=0.0
  -> Calculation: 0.0 - 0.000575 * 909 = -0.522675
------------- 09-16-29-54-992160 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.625000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000800 -> λ_new=0.000825
Lambda Change: Δλ = 0.000025
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=1291, accuracy=1.0
  -> Calculation: 1.0 - 0.000825 * 1291 = -0.065075
Completion 1: length=703, accuracy=0.0
  -> Calculation: 0.0 - 0.000825 * 703 = -0.579975
Completion 2: length=1218, accuracy=1.0
  -> Calculation: 1.0 - 0.000825 * 1218 = -0.004850
Completion 3: length=950, accuracy=1.0
  -> Calculation: 1.0 - 0.000825 * 950 = 0.216250
Completion 4: length=446, accuracy=1.0
  -> Calculation: 1.0 - 0.000825 * 446 = 0.632050
Completion 5: length=562, accuracy=0.0
  -> Calculation: 0.0 - 0.000825 * 562 = -0.463650
Completion 6: length=651, accuracy=1.0
  -> Calculation: 1.0 - 0.000825 * 651 = 0.462925
Completion 7: length=873, accuracy=0.0
  -> Calculation: 0.0 - 0.000825 * 873 = -0.720225
------------- 09-16-30-12-506332 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.250000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001575 -> λ_new=0.001225
Lambda Change: Δλ = -0.000350
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=1115, accuracy=0.0
  -> Calculation: 0.0 - 0.001225 * 1115 = -1.365875
Completion 1: length=1108, accuracy=0.0
  -> Calculation: 0.0 - 0.001225 * 1108 = -1.357300
Completion 2: length=617, accuracy=0.0
  -> Calculation: 0.0 - 0.001225 * 617 = -0.755825
Completion 3: length=1404, accuracy=0.0
  -> Calculation: 0.0 - 0.001225 * 1404 = -1.719900
Completion 4: length=933, accuracy=1.0
  -> Calculation: 1.0 - 0.001225 * 933 = -0.142925
Completion 5: length=1061, accuracy=0.0
  -> Calculation: 0.0 - 0.001225 * 1061 = -1.299725
Completion 6: length=465, accuracy=0.0
  -> Calculation: 0.0 - 0.001225 * 465 = -0.569625
Completion 7: length=721, accuracy=1.0
  -> Calculation: 1.0 - 0.001225 * 721 = 0.116775
------------- 09-16-30-12-521861 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 0.625000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000825 -> λ_new=0.000850
Lambda Change: Δλ = 0.000025
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=864, accuracy=1.0
  -> Calculation: 1.0 - 0.000850 * 864 = 0.265600
Completion 1: length=636, accuracy=0.0
  -> Calculation: 0.0 - 0.000850 * 636 = -0.540600
Completion 2: length=618, accuracy=1.0
  -> Calculation: 1.0 - 0.000850 * 618 = 0.474700
Completion 3: length=705, accuracy=0.0
  -> Calculation: 0.0 - 0.000850 * 705 = -0.599250
Completion 4: length=625, accuracy=1.0
  -> Calculation: 1.0 - 0.000850 * 625 = 0.468750
Completion 5: length=691, accuracy=1.0
  -> Calculation: 1.0 - 0.000850 * 691 = 0.412650
Completion 6: length=2033, accuracy=0.0
  -> Calculation: 0.0 - 0.000850 * 2033 = -1.728050
Completion 7: length=1143, accuracy=1.0
  -> Calculation: 1.0 - 0.000850 * 1143 = 0.028450
------------- 09-16-30-12-578539 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.500000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000325 -> λ_new=0.000225
Lambda Change: Δλ = -0.000100
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=760, accuracy=0.0
  -> Calculation: 0.0 - 0.000225 * 760 = -0.171000
Completion 1: length=945, accuracy=1.0
  -> Calculation: 1.0 - 0.000225 * 945 = 0.787375
Completion 2: length=1339, accuracy=0.0
  -> Calculation: 0.0 - 0.000225 * 1339 = -0.301275
Completion 3: length=780, accuracy=1.0
  -> Calculation: 1.0 - 0.000225 * 780 = 0.824500
Completion 4: length=652, accuracy=0.0
  -> Calculation: 0.0 - 0.000225 * 652 = -0.146700
Completion 5: length=745, accuracy=0.0
  -> Calculation: 0.0 - 0.000225 * 745 = -0.167625
Completion 6: length=668, accuracy=1.0
  -> Calculation: 1.0 - 0.000225 * 668 = 0.849700
Completion 7: length=1271, accuracy=1.0
  -> Calculation: 1.0 - 0.000225 * 1271 = 0.714025
------------- 09-16-30-12-747167 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 1.000000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000575 -> λ_new=0.000975
Lambda Change: Δλ = 0.000400
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=721, accuracy=1.0
  -> Calculation: 1.0 - 0.000975 * 721 = 0.297025
Completion 1: length=1196, accuracy=1.0
  -> Calculation: 1.0 - 0.000975 * 1196 = -0.166100
Completion 2: length=1610, accuracy=1.0
  -> Calculation: 1.0 - 0.000975 * 1610 = -0.569750
Completion 3: length=866, accuracy=1.0
  -> Calculation: 1.0 - 0.000975 * 866 = 0.155650
Completion 4: length=1047, accuracy=1.0
  -> Calculation: 1.0 - 0.000975 * 1047 = -0.020825
Completion 5: length=1215, accuracy=1.0
  -> Calculation: 1.0 - 0.000975 * 1215 = -0.184625
Completion 6: length=698, accuracy=1.0
  -> Calculation: 1.0 - 0.000975 * 698 = 0.319450
Completion 7: length=976, accuracy=1.0
  -> Calculation: 1.0 - 0.000975 * 976 = 0.048400
------------- 09-16-30-31-541632 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.000000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000225 -> λ_new=0.000000
Lambda Change: Δλ = -0.000600
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=813, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 813 = 0.000000
Completion 1: length=970, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 970 = 0.000000
Completion 2: length=614, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 614 = 0.000000
Completion 3: length=880, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 880 = 0.000000
Completion 4: length=702, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 702 = 0.000000
Completion 5: length=845, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 845 = 0.000000
Completion 6: length=406, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 406 = 0.000000
Completion 7: length=728, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 728 = 0.000000
------------- 09-16-30-31-545456 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.125000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001225 -> λ_new=0.000750
Lambda Change: Δλ = -0.000475
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=783, accuracy=0.0
  -> Calculation: 0.0 - 0.000750 * 783 = -0.587250
Completion 1: length=668, accuracy=0.0
  -> Calculation: 0.0 - 0.000750 * 668 = -0.501000
Completion 2: length=977, accuracy=0.0
  -> Calculation: 0.0 - 0.000750 * 977 = -0.732750
Completion 3: length=648, accuracy=0.0
  -> Calculation: 0.0 - 0.000750 * 648 = -0.486000
Completion 4: length=757, accuracy=0.0
  -> Calculation: 0.0 - 0.000750 * 757 = -0.567750
Completion 5: length=947, accuracy=0.0
  -> Calculation: 0.0 - 0.000750 * 947 = -0.710250
Completion 6: length=726, accuracy=0.0
  -> Calculation: 0.0 - 0.000750 * 726 = -0.544500
Completion 7: length=1282, accuracy=1.0
  -> Calculation: 1.0 - 0.000750 * 1282 = 0.038500
------------- 09-16-30-31-552973 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 0.875000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000850 -> λ_new=0.001125
Lambda Change: Δλ = 0.000275
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=974, accuracy=1.0
  -> Calculation: 1.0 - 0.001125 * 974 = -0.095750
Completion 1: length=1447, accuracy=1.0
  -> Calculation: 1.0 - 0.001125 * 1447 = -0.627875
Completion 2: length=707, accuracy=1.0
  -> Calculation: 1.0 - 0.001125 * 707 = 0.204625
Completion 3: length=1179, accuracy=1.0
  -> Calculation: 1.0 - 0.001125 * 1179 = -0.326375
Completion 4: length=1041, accuracy=1.0
  -> Calculation: 1.0 - 0.001125 * 1041 = -0.171125
Completion 5: length=725, accuracy=0.0
  -> Calculation: 0.0 - 0.001125 * 725 = -0.815625
Completion 6: length=517, accuracy=1.0
  -> Calculation: 1.0 - 0.001125 * 517 = 0.418375
Completion 7: length=1088, accuracy=1.0
  -> Calculation: 1.0 - 0.001125 * 1088 = -0.224000
------------- 09-16-30-31-596787 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.000000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000975 -> λ_new=0.000375
Lambda Change: Δλ = -0.000600
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=1831, accuracy=0.0
  -> Calculation: 0.0 - 0.000375 * 1831 = -0.686625
Completion 1: length=1146, accuracy=0.0
  -> Calculation: 0.0 - 0.000375 * 1146 = -0.429750
Completion 2: length=714, accuracy=0.0
  -> Calculation: 0.0 - 0.000375 * 714 = -0.267750
Completion 3: length=202, accuracy=0.0
  -> Calculation: 0.0 - 0.000375 * 202 = -0.075750
Completion 4: length=963, accuracy=0.0
  -> Calculation: 0.0 - 0.000375 * 963 = -0.361125
Completion 5: length=518, accuracy=0.0
  -> Calculation: 0.0 - 0.000375 * 518 = -0.194250
Completion 6: length=961, accuracy=0.0
  -> Calculation: 0.0 - 0.000375 * 961 = -0.360375
Completion 7: length=1004, accuracy=0.0
  -> Calculation: 0.0 - 0.000375 * 1004 = -0.376500
------------- 09-16-30-53-530606 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.375000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000000 -> λ_new=0.000000
Lambda Change: Δλ = -0.000225
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=507, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 507 = 0.000000
Completion 1: length=506, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 506 = 0.000000
Completion 2: length=430, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 430 = 0.000000
Completion 3: length=545, accuracy=1.0
  -> Calculation: 1.0 - 0.000000 * 545 = 1.000000
Completion 4: length=1181, accuracy=1.0
  -> Calculation: 1.0 - 0.000000 * 1181 = 1.000000
Completion 5: length=734, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 734 = 0.000000
Completion 6: length=1022, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 1022 = 0.000000
Completion 7: length=1017, accuracy=1.0
  -> Calculation: 1.0 - 0.000000 * 1017 = 1.000000
------------- 09-16-30-53-540264 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.625000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000750 -> λ_new=0.000775
Lambda Change: Δλ = 0.000025
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=757, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 757 = 0.413325
Completion 1: length=830, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 830 = -0.643250
Completion 2: length=704, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 704 = 0.454400
Completion 3: length=556, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 556 = 0.569100
Completion 4: length=1004, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 1004 = 0.221900
Completion 5: length=715, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 715 = -0.554125
Completion 6: length=858, accuracy=1.0
  -> Calculation: 1.0 - 0.000775 * 858 = 0.335050
Completion 7: length=825, accuracy=0.0
  -> Calculation: 0.0 - 0.000775 * 825 = -0.639375
------------- 09-16-30-53-558213 Adaptive Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy (acc_t): 1.000000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.001125 -> λ_new=0.001525
Lambda Change: Δλ = 0.000400
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=670, accuracy=1.0
  -> Calculation: 1.0 - 0.001525 * 670 = -0.021750
Completion 1: length=1166, accuracy=1.0
  -> Calculation: 1.0 - 0.001525 * 1166 = -0.778150
Completion 2: length=875, accuracy=1.0
  -> Calculation: 1.0 - 0.001525 * 875 = -0.334375
Completion 3: length=2099, accuracy=1.0
  -> Calculation: 1.0 - 0.001525 * 2099 = -2.200975
Completion 4: length=350, accuracy=1.0
  -> Calculation: 1.0 - 0.001525 * 350 = 0.466250
Completion 5: length=990, accuracy=1.0
  -> Calculation: 1.0 - 0.001525 * 990 = -0.509750
Completion 6: length=1273, accuracy=1.0
  -> Calculation: 1.0 - 0.001525 * 1273 = -0.941325
Completion 7: length=819, accuracy=1.0
  -> Calculation: 1.0 - 0.001525 * 819 = -0.248975
------------- 09-16-30-53-585708 Adaptive Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy (acc_t): 0.125000
Reference Accuracy (acc_ref): 0.600000
Learning Rate (η): 0.001000
Lambda Update: λ_old=0.000375 -> λ_new=0.000000
Lambda Change: Δλ = -0.000475
Formula: R_λ(x, y) = 𝟙{y = y*} - λ_t · len(y)
Lambda Update: λ_{t+1} = max(0, λ_t + η · (acc_t - acc_ref))
Completion 0: length=1217, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 1217 = 0.000000
Completion 1: length=527, accuracy=1.0
  -> Calculation: 1.0 - 0.000000 * 527 = 1.000000
Completion 2: length=294, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 294 = 0.000000
Completion 3: length=1347, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 1347 = 0.000000
Completion 4: length=1392, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 1392 = 0.000000
Completion 5: length=460, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 460 = 0.000000
Completion 6: length=1199, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 1199 = 0.000000
Completion 7: length=692, accuracy=0.0
  -> Calculation: 0.0 - 0.000000 * 692 = 0.000000
