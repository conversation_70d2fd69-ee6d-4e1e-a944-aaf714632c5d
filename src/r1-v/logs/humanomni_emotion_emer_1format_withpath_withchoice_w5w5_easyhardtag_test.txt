------------- 05-23-56-57-953097 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 725.375
Completion 0: length=696, L/Lavg=0.960, ra=0.0, reward=-0.040
Completion 1: length=652, L/Lavg=0.899, ra=0.0, reward=-0.101
Completion 2: length=909, L/Lavg=1.253, ra=0.0, reward=0.253
Completion 3: length=544, L/Lavg=0.750, ra=0.0, reward=-0.250
Completion 4: length=873, L/Lavg=1.204, ra=0.0, reward=0.204
Completion 5: length=514, L/Lavg=0.709, ra=0.0, reward=-0.291
Completion 6: length=891, L/Lavg=1.228, ra=0.0, reward=0.228
Completion 7: length=724, L/Lavg=0.998, ra=0.0, reward=-0.002
------------- 05-23-56-57-953739 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 743.75
Completion 0: length=669, L/Lavg=0.899, ra=0.0, reward=-0.101
Completion 1: length=795, L/Lavg=1.069, ra=0.0, reward=0.069
Completion 2: length=616, L/Lavg=0.828, ra=0.0, reward=-0.172
Completion 3: length=700, L/Lavg=0.941, ra=0.0, reward=-0.059
Completion 4: length=849, L/Lavg=1.142, ra=0.0, reward=0.142
Completion 5: length=548, L/Lavg=0.737, ra=0.0, reward=-0.263
Completion 6: length=885, L/Lavg=1.190, ra=0.0, reward=0.190
Completion 7: length=888, L/Lavg=1.194, ra=0.0, reward=0.194
------------- 05-23-56-57-954841 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.375
Average Length (Lavg): 887.5
Completion 0: length=531, L/Lavg=0.598, ra=0.0, reward=0.000
Completion 1: length=835, L/Lavg=0.941, ra=0.0, reward=0.000
Completion 2: length=784, L/Lavg=0.883, ra=0.0, reward=0.000
Completion 3: length=1199, L/Lavg=1.351, ra=1.0, reward=-0.351
Completion 4: length=1390, L/Lavg=1.566, ra=1.0, reward=-0.566
Completion 5: length=1202, L/Lavg=1.354, ra=1.0, reward=-0.354
Completion 6: length=846, L/Lavg=0.953, ra=0.0, reward=0.000
Completion 7: length=313, L/Lavg=0.353, ra=0.0, reward=0.000
------------- 05-23-56-57-956905 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1034.25
Completion 0: length=1229, L/Lavg=1.188, ra=0.0, reward=0.188
Completion 1: length=1204, L/Lavg=1.164, ra=0.0, reward=0.164
Completion 2: length=899, L/Lavg=0.869, ra=0.0, reward=-0.131
Completion 3: length=1371, L/Lavg=1.326, ra=0.0, reward=0.326
Completion 4: length=1413, L/Lavg=1.366, ra=0.0, reward=0.366
Completion 5: length=952, L/Lavg=0.920, ra=0.0, reward=-0.080
Completion 6: length=665, L/Lavg=0.643, ra=0.0, reward=-0.357
Completion 7: length=541, L/Lavg=0.523, ra=0.0, reward=-0.477
------------- 05-23-57-16-582677 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 753.0
Completion 0: length=714, L/Lavg=0.948, ra=0.0, reward=-0.052
Completion 1: length=1271, L/Lavg=1.688, ra=0.0, reward=0.688
Completion 2: length=705, L/Lavg=0.936, ra=0.0, reward=-0.064
Completion 3: length=941, L/Lavg=1.250, ra=0.0, reward=0.250
Completion 4: length=410, L/Lavg=0.544, ra=0.0, reward=-0.456
Completion 5: length=717, L/Lavg=0.952, ra=0.0, reward=-0.048
Completion 6: length=649, L/Lavg=0.862, ra=0.0, reward=-0.138
Completion 7: length=617, L/Lavg=0.819, ra=0.0, reward=-0.181
------------- 05-23-57-16-677196 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 746.75
Completion 0: length=697, L/Lavg=0.933, ra=0.0, reward=-0.067
Completion 1: length=691, L/Lavg=0.925, ra=0.0, reward=-0.075
Completion 2: length=569, L/Lavg=0.762, ra=0.0, reward=-0.238
Completion 3: length=782, L/Lavg=1.047, ra=0.0, reward=0.047
Completion 4: length=834, L/Lavg=1.117, ra=0.0, reward=0.117
Completion 5: length=973, L/Lavg=1.303, ra=0.0, reward=0.303
Completion 6: length=670, L/Lavg=0.897, ra=0.0, reward=-0.103
Completion 7: length=758, L/Lavg=1.015, ra=0.0, reward=0.015
------------- 05-23-57-16-683941 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 661.25
Completion 0: length=599, L/Lavg=0.906, ra=0.0, reward=-0.094
Completion 1: length=757, L/Lavg=1.145, ra=0.0, reward=0.145
Completion 2: length=652, L/Lavg=0.986, ra=0.0, reward=-0.014
Completion 3: length=746, L/Lavg=1.128, ra=0.0, reward=0.128
Completion 4: length=846, L/Lavg=1.279, ra=0.0, reward=0.279
Completion 5: length=498, L/Lavg=0.753, ra=0.0, reward=-0.247
Completion 6: length=355, L/Lavg=0.537, ra=0.0, reward=-0.463
Completion 7: length=837, L/Lavg=1.266, ra=0.0, reward=0.266
------------- 05-23-57-16-881269 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.750
Average Length (Lavg): 991.875
Completion 0: length=1374, L/Lavg=1.385, ra=1.0, reward=-0.385
Completion 1: length=643, L/Lavg=0.648, ra=1.0, reward=0.352
Completion 2: length=957, L/Lavg=0.965, ra=1.0, reward=0.035
Completion 3: length=1418, L/Lavg=1.430, ra=1.0, reward=-0.430
Completion 4: length=747, L/Lavg=0.753, ra=1.0, reward=0.247
Completion 5: length=1236, L/Lavg=1.246, ra=0.0, reward=0.000
Completion 6: length=709, L/Lavg=0.715, ra=1.0, reward=0.285
Completion 7: length=851, L/Lavg=0.858, ra=0.0, reward=0.000
------------- 05-23-57-33-514011 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.375
Average Length (Lavg): 617.625
Completion 0: length=626, L/Lavg=1.014, ra=0.0, reward=0.014
Completion 1: length=558, L/Lavg=0.903, ra=0.0, reward=-0.097
Completion 2: length=605, L/Lavg=0.980, ra=1.0, reward=0.000
Completion 3: length=519, L/Lavg=0.840, ra=1.0, reward=0.000
Completion 4: length=729, L/Lavg=1.180, ra=0.0, reward=0.180
Completion 5: length=592, L/Lavg=0.959, ra=0.0, reward=-0.041
Completion 6: length=679, L/Lavg=1.099, ra=1.0, reward=0.000
Completion 7: length=633, L/Lavg=1.025, ra=0.0, reward=0.025
------------- 05-23-57-33-522167 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.250
Average Length (Lavg): 1010.25
Completion 0: length=1094, L/Lavg=1.083, ra=0.0, reward=0.083
Completion 1: length=891, L/Lavg=0.882, ra=0.0, reward=-0.118
Completion 2: length=676, L/Lavg=0.669, ra=1.0, reward=0.000
Completion 3: length=812, L/Lavg=0.804, ra=1.0, reward=0.000
Completion 4: length=1424, L/Lavg=1.410, ra=0.0, reward=0.410
Completion 5: length=1524, L/Lavg=1.509, ra=0.0, reward=0.509
Completion 6: length=720, L/Lavg=0.713, ra=0.0, reward=-0.287
Completion 7: length=941, L/Lavg=0.931, ra=0.0, reward=-0.069
------------- 05-23-57-33-525504 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.250
Average Length (Lavg): 826.875
Completion 0: length=524, L/Lavg=0.634, ra=0.0, reward=-0.366
Completion 1: length=655, L/Lavg=0.792, ra=1.0, reward=0.000
Completion 2: length=939, L/Lavg=1.136, ra=1.0, reward=0.000
Completion 3: length=501, L/Lavg=0.606, ra=0.0, reward=-0.394
Completion 4: length=827, L/Lavg=1.000, ra=0.0, reward=0.000
Completion 5: length=819, L/Lavg=0.990, ra=0.0, reward=-0.010
Completion 6: length=1166, L/Lavg=1.410, ra=0.0, reward=0.410
Completion 7: length=1184, L/Lavg=1.432, ra=0.0, reward=0.432
------------- 05-23-57-33-790150 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1046.0
Completion 0: length=422, L/Lavg=0.403, ra=0.0, reward=-0.597
Completion 1: length=755, L/Lavg=0.722, ra=0.0, reward=-0.278
Completion 2: length=977, L/Lavg=0.934, ra=0.0, reward=-0.066
Completion 3: length=696, L/Lavg=0.665, ra=0.0, reward=-0.335
Completion 4: length=1706, L/Lavg=1.631, ra=0.0, reward=0.631
Completion 5: length=958, L/Lavg=0.916, ra=0.0, reward=-0.084
Completion 6: length=1229, L/Lavg=1.175, ra=0.0, reward=0.175
Completion 7: length=1625, L/Lavg=1.554, ra=0.0, reward=0.554
------------- 05-23-57-48-353291 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.625
Average Length (Lavg): 818.5
Completion 0: length=879, L/Lavg=1.074, ra=0.0, reward=0.074
Completion 1: length=460, L/Lavg=0.562, ra=1.0, reward=0.000
Completion 2: length=902, L/Lavg=1.102, ra=1.0, reward=0.000
Completion 3: length=727, L/Lavg=0.888, ra=1.0, reward=0.000
Completion 4: length=854, L/Lavg=1.043, ra=0.0, reward=0.043
Completion 5: length=1070, L/Lavg=1.307, ra=0.0, reward=0.307
Completion 6: length=736, L/Lavg=0.899, ra=1.0, reward=0.000
Completion 7: length=920, L/Lavg=1.124, ra=1.0, reward=0.000
------------- 05-23-57-48-354007 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.125
Average Length (Lavg): 787.0
Completion 0: length=377, L/Lavg=0.479, ra=0.0, reward=0.000
Completion 1: length=746, L/Lavg=0.948, ra=1.0, reward=0.052
Completion 2: length=675, L/Lavg=0.858, ra=0.0, reward=0.000
Completion 3: length=817, L/Lavg=1.038, ra=0.0, reward=0.000
Completion 4: length=704, L/Lavg=0.895, ra=0.0, reward=0.000
Completion 5: length=1308, L/Lavg=1.662, ra=0.0, reward=0.000
Completion 6: length=925, L/Lavg=1.175, ra=0.0, reward=0.000
Completion 7: length=744, L/Lavg=0.945, ra=0.0, reward=0.000
------------- 05-23-57-48-355635 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.625
Average Length (Lavg): 853.5
Completion 0: length=576, L/Lavg=0.675, ra=0.0, reward=0.000
Completion 1: length=537, L/Lavg=0.629, ra=1.0, reward=0.371
Completion 2: length=1101, L/Lavg=1.290, ra=1.0, reward=-0.290
Completion 3: length=1151, L/Lavg=1.349, ra=1.0, reward=-0.349
Completion 4: length=915, L/Lavg=1.072, ra=0.0, reward=0.000
Completion 5: length=725, L/Lavg=0.849, ra=1.0, reward=0.151
Completion 6: length=756, L/Lavg=0.886, ra=1.0, reward=0.114
Completion 7: length=1067, L/Lavg=1.250, ra=0.0, reward=0.000
------------- 05-23-57-48-361645 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 914.5
Completion 0: length=1270, L/Lavg=1.389, ra=0.0, reward=0.389
Completion 1: length=656, L/Lavg=0.717, ra=0.0, reward=-0.283
Completion 2: length=900, L/Lavg=0.984, ra=0.0, reward=-0.016
Completion 3: length=1084, L/Lavg=1.185, ra=0.0, reward=0.185
Completion 4: length=857, L/Lavg=0.937, ra=0.0, reward=-0.063
Completion 5: length=582, L/Lavg=0.636, ra=0.0, reward=-0.364
Completion 6: length=1126, L/Lavg=1.231, ra=0.0, reward=0.231
Completion 7: length=841, L/Lavg=0.920, ra=0.0, reward=-0.080
------------- 05-23-58-06-060319 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.125
Average Length (Lavg): 824.125
Completion 0: length=638, L/Lavg=0.774, ra=0.0, reward=-0.226
Completion 1: length=590, L/Lavg=0.716, ra=0.0, reward=-0.284
Completion 2: length=1360, L/Lavg=1.650, ra=0.0, reward=0.650
Completion 3: length=532, L/Lavg=0.646, ra=0.0, reward=-0.354
Completion 4: length=1026, L/Lavg=1.245, ra=0.0, reward=0.245
Completion 5: length=1074, L/Lavg=1.303, ra=0.0, reward=0.303
Completion 6: length=748, L/Lavg=0.908, ra=0.0, reward=-0.092
Completion 7: length=625, L/Lavg=0.758, ra=1.0, reward=0.000
------------- 05-23-58-06-060333 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.625
Average Length (Lavg): 737.75
Completion 0: length=713, L/Lavg=0.966, ra=1.0, reward=0.034
Completion 1: length=663, L/Lavg=0.899, ra=0.0, reward=0.000
Completion 2: length=674, L/Lavg=0.914, ra=1.0, reward=0.086
Completion 3: length=592, L/Lavg=0.802, ra=1.0, reward=0.198
Completion 4: length=1134, L/Lavg=1.537, ra=0.0, reward=0.000
Completion 5: length=1011, L/Lavg=1.370, ra=1.0, reward=-0.370
Completion 6: length=387, L/Lavg=0.525, ra=0.0, reward=0.000
Completion 7: length=728, L/Lavg=0.987, ra=1.0, reward=0.013
------------- 05-23-58-06-061762 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.125
Average Length (Lavg): 964.25
Completion 0: length=969, L/Lavg=1.005, ra=1.0, reward=0.000
Completion 1: length=892, L/Lavg=0.925, ra=0.0, reward=-0.075
Completion 2: length=782, L/Lavg=0.811, ra=0.0, reward=-0.189
Completion 3: length=1225, L/Lavg=1.270, ra=0.0, reward=0.270
Completion 4: length=922, L/Lavg=0.956, ra=0.0, reward=-0.044
Completion 5: length=789, L/Lavg=0.818, ra=0.0, reward=-0.182
Completion 6: length=1444, L/Lavg=1.498, ra=0.0, reward=0.498
Completion 7: length=691, L/Lavg=0.717, ra=0.0, reward=-0.283
------------- 05-23-58-06-327663 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1004.75
Completion 0: length=766, L/Lavg=0.762, ra=0.0, reward=-0.238
Completion 1: length=1227, L/Lavg=1.221, ra=0.0, reward=0.221
Completion 2: length=528, L/Lavg=0.526, ra=0.0, reward=-0.474
Completion 3: length=949, L/Lavg=0.945, ra=0.0, reward=-0.055
Completion 4: length=868, L/Lavg=0.864, ra=0.0, reward=-0.136
Completion 5: length=1946, L/Lavg=1.937, ra=0.0, reward=0.937
Completion 6: length=598, L/Lavg=0.595, ra=0.0, reward=-0.405
Completion 7: length=1156, L/Lavg=1.151, ra=0.0, reward=0.151
------------- 05-23-58-24-525605 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.250
Average Length (Lavg): 705.875
Completion 0: length=308, L/Lavg=0.436, ra=1.0, reward=0.000
Completion 1: length=558, L/Lavg=0.791, ra=0.0, reward=-0.209
Completion 2: length=505, L/Lavg=0.715, ra=0.0, reward=-0.285
Completion 3: length=568, L/Lavg=0.805, ra=0.0, reward=-0.195
Completion 4: length=1189, L/Lavg=1.684, ra=0.0, reward=0.684
Completion 5: length=1249, L/Lavg=1.769, ra=0.0, reward=0.769
Completion 6: length=697, L/Lavg=0.987, ra=0.0, reward=-0.013
Completion 7: length=573, L/Lavg=0.812, ra=1.0, reward=0.000
------------- 05-23-58-24-528703 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 686.75
Completion 0: length=797, L/Lavg=1.161, ra=0.0, reward=0.161
Completion 1: length=561, L/Lavg=0.817, ra=0.0, reward=-0.183
Completion 2: length=630, L/Lavg=0.917, ra=0.0, reward=-0.083
Completion 3: length=673, L/Lavg=0.980, ra=0.0, reward=-0.020
Completion 4: length=458, L/Lavg=0.667, ra=0.0, reward=-0.333
Completion 5: length=944, L/Lavg=1.375, ra=0.0, reward=0.375
Completion 6: length=595, L/Lavg=0.866, ra=0.0, reward=-0.134
Completion 7: length=836, L/Lavg=1.217, ra=0.0, reward=0.217
------------- 05-23-58-24-533068 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 943.75
Completion 0: length=449, L/Lavg=0.476, ra=0.0, reward=-0.524
Completion 1: length=647, L/Lavg=0.686, ra=0.0, reward=-0.314
Completion 2: length=583, L/Lavg=0.618, ra=0.0, reward=-0.382
Completion 3: length=967, L/Lavg=1.025, ra=0.0, reward=0.025
Completion 4: length=867, L/Lavg=0.919, ra=0.0, reward=-0.081
Completion 5: length=1996, L/Lavg=2.115, ra=0.0, reward=1.000
Completion 6: length=1283, L/Lavg=1.359, ra=0.0, reward=0.359
Completion 7: length=758, L/Lavg=0.803, ra=0.0, reward=-0.197
------------- 05-23-58-24-848248 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.625
Average Length (Lavg): 950.125
Completion 0: length=679, L/Lavg=0.715, ra=0.0, reward=0.000
Completion 1: length=711, L/Lavg=0.748, ra=0.0, reward=0.000
Completion 2: length=706, L/Lavg=0.743, ra=1.0, reward=0.257
Completion 3: length=1592, L/Lavg=1.676, ra=1.0, reward=-0.676
Completion 4: length=549, L/Lavg=0.578, ra=1.0, reward=0.422
Completion 5: length=991, L/Lavg=1.043, ra=0.0, reward=0.000
Completion 6: length=1146, L/Lavg=1.206, ra=1.0, reward=-0.206
Completion 7: length=1227, L/Lavg=1.291, ra=1.0, reward=-0.291
------------- 05-23-58-40-954235 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1030.875
Completion 0: length=1046, L/Lavg=1.015, ra=0.0, reward=0.015
Completion 1: length=713, L/Lavg=0.692, ra=0.0, reward=-0.308
Completion 2: length=870, L/Lavg=0.844, ra=0.0, reward=-0.156
Completion 3: length=680, L/Lavg=0.660, ra=0.0, reward=-0.340
Completion 4: length=938, L/Lavg=0.910, ra=0.0, reward=-0.090
Completion 5: length=1635, L/Lavg=1.586, ra=0.0, reward=0.586
Completion 6: length=1458, L/Lavg=1.414, ra=0.0, reward=0.414
Completion 7: length=907, L/Lavg=0.880, ra=0.0, reward=-0.120
------------- 05-23-58-40-954978 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.875
Average Length (Lavg): 764.75
Completion 0: length=825, L/Lavg=1.079, ra=1.0, reward=-0.079
Completion 1: length=600, L/Lavg=0.785, ra=1.0, reward=0.215
Completion 2: length=707, L/Lavg=0.924, ra=1.0, reward=0.076
Completion 3: length=582, L/Lavg=0.761, ra=1.0, reward=0.239
Completion 4: length=1049, L/Lavg=1.372, ra=0.0, reward=0.000
Completion 5: length=872, L/Lavg=1.140, ra=1.0, reward=-0.140
Completion 6: length=722, L/Lavg=0.944, ra=1.0, reward=0.056
Completion 7: length=761, L/Lavg=0.995, ra=1.0, reward=0.005
------------- 05-23-58-40-963948 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.375
Average Length (Lavg): 785.25
Completion 0: length=1602, L/Lavg=2.040, ra=0.0, reward=0.000
Completion 1: length=763, L/Lavg=0.972, ra=1.0, reward=0.028
Completion 2: length=817, L/Lavg=1.040, ra=0.0, reward=0.000
Completion 3: length=651, L/Lavg=0.829, ra=1.0, reward=0.171
Completion 4: length=737, L/Lavg=0.939, ra=0.0, reward=0.000
Completion 5: length=745, L/Lavg=0.949, ra=0.0, reward=0.000
Completion 6: length=585, L/Lavg=0.745, ra=1.0, reward=0.255
Completion 7: length=382, L/Lavg=0.486, ra=0.0, reward=0.000
------------- 05-23-58-40-971751 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1163.125
Completion 0: length=1335, L/Lavg=1.148, ra=0.0, reward=0.148
Completion 1: length=1193, L/Lavg=1.026, ra=0.0, reward=0.026
Completion 2: length=1115, L/Lavg=0.959, ra=0.0, reward=-0.041
Completion 3: length=686, L/Lavg=0.590, ra=0.0, reward=-0.410
Completion 4: length=772, L/Lavg=0.664, ra=0.0, reward=-0.336
Completion 5: length=1404, L/Lavg=1.207, ra=0.0, reward=0.207
Completion 6: length=1455, L/Lavg=1.251, ra=0.0, reward=0.251
Completion 7: length=1345, L/Lavg=1.156, ra=0.0, reward=0.156
------------- 05-23-58-56-183080 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 704.875
Completion 0: length=541, L/Lavg=0.768, ra=0.0, reward=-0.232
Completion 1: length=793, L/Lavg=1.125, ra=0.0, reward=0.125
Completion 2: length=637, L/Lavg=0.904, ra=0.0, reward=-0.096
Completion 3: length=861, L/Lavg=1.221, ra=0.0, reward=0.221
Completion 4: length=636, L/Lavg=0.902, ra=0.0, reward=-0.098
Completion 5: length=774, L/Lavg=1.098, ra=0.0, reward=0.098
Completion 6: length=775, L/Lavg=1.099, ra=0.0, reward=0.099
Completion 7: length=622, L/Lavg=0.882, ra=0.0, reward=-0.118
------------- 05-23-58-56-189873 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1016.75
Completion 0: length=871, L/Lavg=0.857, ra=0.0, reward=-0.143
Completion 1: length=1489, L/Lavg=1.464, ra=0.0, reward=0.464
Completion 2: length=751, L/Lavg=0.739, ra=0.0, reward=-0.261
Completion 3: length=1044, L/Lavg=1.027, ra=0.0, reward=0.027
Completion 4: length=1185, L/Lavg=1.165, ra=0.0, reward=0.165
Completion 5: length=743, L/Lavg=0.731, ra=0.0, reward=-0.269
Completion 6: length=1083, L/Lavg=1.065, ra=0.0, reward=0.065
Completion 7: length=968, L/Lavg=0.952, ra=0.0, reward=-0.048
------------- 05-23-58-56-190692 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1063.625
Completion 0: length=1159, L/Lavg=1.090, ra=0.0, reward=0.090
Completion 1: length=660, L/Lavg=0.621, ra=0.0, reward=-0.379
Completion 2: length=961, L/Lavg=0.904, ra=0.0, reward=-0.096
Completion 3: length=695, L/Lavg=0.653, ra=0.0, reward=-0.347
Completion 4: length=1005, L/Lavg=0.945, ra=0.0, reward=-0.055
Completion 5: length=1396, L/Lavg=1.312, ra=0.0, reward=0.312
Completion 6: length=1189, L/Lavg=1.118, ra=0.0, reward=0.118
Completion 7: length=1444, L/Lavg=1.358, ra=0.0, reward=0.358
------------- 05-23-58-56-201014 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 859.25
Completion 0: length=1510, L/Lavg=1.757, ra=0.0, reward=0.757
Completion 1: length=390, L/Lavg=0.454, ra=0.0, reward=-0.546
Completion 2: length=993, L/Lavg=1.156, ra=0.0, reward=0.156
Completion 3: length=875, L/Lavg=1.018, ra=0.0, reward=0.018
Completion 4: length=1088, L/Lavg=1.266, ra=0.0, reward=0.266
Completion 5: length=757, L/Lavg=0.881, ra=0.0, reward=-0.119
Completion 6: length=704, L/Lavg=0.819, ra=0.0, reward=-0.181
Completion 7: length=557, L/Lavg=0.648, ra=0.0, reward=-0.352
------------- 05-23-59-11-905110 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 849.75
Completion 0: length=1333, L/Lavg=1.569, ra=0.0, reward=0.569
Completion 1: length=913, L/Lavg=1.074, ra=0.0, reward=0.074
Completion 2: length=804, L/Lavg=0.946, ra=0.0, reward=-0.054
Completion 3: length=706, L/Lavg=0.831, ra=0.0, reward=-0.169
Completion 4: length=779, L/Lavg=0.917, ra=0.0, reward=-0.083
Completion 5: length=706, L/Lavg=0.831, ra=0.0, reward=-0.169
Completion 6: length=666, L/Lavg=0.784, ra=0.0, reward=-0.216
Completion 7: length=891, L/Lavg=1.049, ra=0.0, reward=0.049
------------- 05-23-59-11-905085 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.250
Average Length (Lavg): 859.125
Completion 0: length=562, L/Lavg=0.654, ra=0.0, reward=-0.346
Completion 1: length=946, L/Lavg=1.101, ra=1.0, reward=0.000
Completion 2: length=560, L/Lavg=0.652, ra=0.0, reward=-0.348
Completion 3: length=956, L/Lavg=1.113, ra=1.0, reward=0.000
Completion 4: length=1127, L/Lavg=1.312, ra=0.0, reward=0.312
Completion 5: length=1411, L/Lavg=1.642, ra=0.0, reward=0.642
Completion 6: length=771, L/Lavg=0.897, ra=0.0, reward=-0.103
Completion 7: length=540, L/Lavg=0.629, ra=0.0, reward=-0.371
------------- 05-23-59-11-910781 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 882.875
Completion 0: length=744, L/Lavg=0.843, ra=0.0, reward=-0.157
Completion 1: length=647, L/Lavg=0.733, ra=0.0, reward=-0.267
Completion 2: length=1603, L/Lavg=1.816, ra=0.0, reward=0.816
Completion 3: length=961, L/Lavg=1.088, ra=0.0, reward=0.088
Completion 4: length=736, L/Lavg=0.834, ra=0.0, reward=-0.166
Completion 5: length=1041, L/Lavg=1.179, ra=0.0, reward=0.179
Completion 6: length=703, L/Lavg=0.796, ra=0.0, reward=-0.204
Completion 7: length=628, L/Lavg=0.711, ra=0.0, reward=-0.289
------------- 05-23-59-11-914729 New Length Reward Debug -------------
Problem Type: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
Group Accuracy: 0.875
Average Length (Lavg): 692.75
Completion 0: length=659, L/Lavg=0.951, ra=1.0, reward=0.049
Completion 1: length=1007, L/Lavg=1.454, ra=1.0, reward=-0.454
Completion 2: length=742, L/Lavg=1.071, ra=1.0, reward=-0.071
Completion 3: length=630, L/Lavg=0.909, ra=1.0, reward=0.091
Completion 4: length=471, L/Lavg=0.680, ra=1.0, reward=0.320
Completion 5: length=666, L/Lavg=0.961, ra=1.0, reward=0.039
Completion 6: length=716, L/Lavg=1.034, ra=1.0, reward=-0.034
Completion 7: length=651, L/Lavg=0.940, ra=0.0, reward=0.000
------------- 05-23-59-29-826677 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 844.0
Completion 0: length=748, L/Lavg=0.886, ra=0.0, reward=-0.114
Completion 1: length=549, L/Lavg=0.650, ra=0.0, reward=-0.350
Completion 2: length=1292, L/Lavg=1.531, ra=0.0, reward=0.531
Completion 3: length=1254, L/Lavg=1.486, ra=0.0, reward=0.486
Completion 4: length=481, L/Lavg=0.570, ra=0.0, reward=-0.430
Completion 5: length=768, L/Lavg=0.910, ra=0.0, reward=-0.090
Completion 6: length=912, L/Lavg=1.081, ra=0.0, reward=0.081
Completion 7: length=748, L/Lavg=0.886, ra=0.0, reward=-0.114
------------- 05-23-59-29-827170 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 709.125
Completion 0: length=1117, L/Lavg=1.575, ra=0.0, reward=0.575
Completion 1: length=730, L/Lavg=1.029, ra=0.0, reward=0.029
Completion 2: length=308, L/Lavg=0.434, ra=0.0, reward=-0.566
Completion 3: length=686, L/Lavg=0.967, ra=0.0, reward=-0.033
Completion 4: length=522, L/Lavg=0.736, ra=0.0, reward=-0.264
Completion 5: length=902, L/Lavg=1.272, ra=0.0, reward=0.272
Completion 6: length=793, L/Lavg=1.118, ra=0.0, reward=0.118
Completion 7: length=615, L/Lavg=0.867, ra=0.0, reward=-0.133
------------- 05-23-59-29-836253 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1120.75
Completion 0: length=1081, L/Lavg=0.965, ra=0.0, reward=-0.035
Completion 1: length=599, L/Lavg=0.534, ra=0.0, reward=-0.466
Completion 2: length=946, L/Lavg=0.844, ra=0.0, reward=-0.156
Completion 3: length=1650, L/Lavg=1.472, ra=0.0, reward=0.472
Completion 4: length=730, L/Lavg=0.651, ra=0.0, reward=-0.349
Completion 5: length=2080, L/Lavg=1.856, ra=0.0, reward=0.856
Completion 6: length=603, L/Lavg=0.538, ra=0.0, reward=-0.462
Completion 7: length=1277, L/Lavg=1.139, ra=0.0, reward=0.139
------------- 05-23-59-29-843923 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 874.0
Completion 0: length=1277, L/Lavg=1.461, ra=0.0, reward=0.461
Completion 1: length=1187, L/Lavg=1.358, ra=0.0, reward=0.358
Completion 2: length=693, L/Lavg=0.793, ra=0.0, reward=-0.207
Completion 3: length=666, L/Lavg=0.762, ra=0.0, reward=-0.238
Completion 4: length=1107, L/Lavg=1.267, ra=0.0, reward=0.267
Completion 5: length=734, L/Lavg=0.840, ra=0.0, reward=-0.160
Completion 6: length=569, L/Lavg=0.651, ra=0.0, reward=-0.349
Completion 7: length=759, L/Lavg=0.868, ra=0.0, reward=-0.132
------------- 05-23-59-49-720740 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 906.5
Completion 0: length=613, L/Lavg=0.676, ra=0.0, reward=-0.324
Completion 1: length=572, L/Lavg=0.631, ra=0.0, reward=-0.369
Completion 2: length=1193, L/Lavg=1.316, ra=0.0, reward=0.316
Completion 3: length=608, L/Lavg=0.671, ra=0.0, reward=-0.329
Completion 4: length=812, L/Lavg=0.896, ra=0.0, reward=-0.104
Completion 5: length=1134, L/Lavg=1.251, ra=0.0, reward=0.251
Completion 6: length=733, L/Lavg=0.809, ra=0.0, reward=-0.191
Completion 7: length=1587, L/Lavg=1.751, ra=0.0, reward=0.751
------------- 05-23-59-49-730728 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 930.625
Completion 0: length=427, L/Lavg=0.459, ra=0.0, reward=-0.541
Completion 1: length=1058, L/Lavg=1.137, ra=0.0, reward=0.137
Completion 2: length=1048, L/Lavg=1.126, ra=0.0, reward=0.126
Completion 3: length=745, L/Lavg=0.801, ra=0.0, reward=-0.199
Completion 4: length=833, L/Lavg=0.895, ra=0.0, reward=-0.105
Completion 5: length=1316, L/Lavg=1.414, ra=0.0, reward=0.414
Completion 6: length=1006, L/Lavg=1.081, ra=0.0, reward=0.081
Completion 7: length=1012, L/Lavg=1.087, ra=0.0, reward=0.087
------------- 05-23-59-49-736234 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1075.375
Completion 0: length=780, L/Lavg=0.725, ra=0.0, reward=-0.275
Completion 1: length=648, L/Lavg=0.603, ra=0.0, reward=-0.397
Completion 2: length=2211, L/Lavg=2.056, ra=0.0, reward=1.000
Completion 3: length=1144, L/Lavg=1.064, ra=0.0, reward=0.064
Completion 4: length=265, L/Lavg=0.246, ra=0.0, reward=-0.754
Completion 5: length=1017, L/Lavg=0.946, ra=0.0, reward=-0.054
Completion 6: length=1374, L/Lavg=1.278, ra=0.0, reward=0.278
Completion 7: length=1164, L/Lavg=1.082, ra=0.0, reward=0.082
------------- 05-23-59-49-739275 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1121.875
Completion 0: length=812, L/Lavg=0.724, ra=0.0, reward=-0.276
Completion 1: length=1655, L/Lavg=1.475, ra=0.0, reward=0.475
Completion 2: length=1429, L/Lavg=1.274, ra=0.0, reward=0.274
Completion 3: length=1735, L/Lavg=1.547, ra=0.0, reward=0.547
Completion 4: length=828, L/Lavg=0.738, ra=0.0, reward=-0.262
Completion 5: length=796, L/Lavg=0.710, ra=0.0, reward=-0.290
Completion 6: length=847, L/Lavg=0.755, ra=0.0, reward=-0.245
Completion 7: length=873, L/Lavg=0.778, ra=0.0, reward=-0.222
------------- 06-00-00-06-822379 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 883.75
Completion 0: length=534, L/Lavg=0.604, ra=0.0, reward=-0.396
Completion 1: length=467, L/Lavg=0.528, ra=0.0, reward=-0.472
Completion 2: length=768, L/Lavg=0.869, ra=0.0, reward=-0.131
Completion 3: length=1170, L/Lavg=1.324, ra=0.0, reward=0.324
Completion 4: length=867, L/Lavg=0.981, ra=0.0, reward=-0.019
Completion 5: length=942, L/Lavg=1.066, ra=0.0, reward=0.066
Completion 6: length=1255, L/Lavg=1.420, ra=0.0, reward=0.420
Completion 7: length=1067, L/Lavg=1.207, ra=0.0, reward=0.207
------------- 06-00-00-06-825267 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 931.25
Completion 0: length=841, L/Lavg=0.903, ra=0.0, reward=-0.097
Completion 1: length=1032, L/Lavg=1.108, ra=0.0, reward=0.108
Completion 2: length=1268, L/Lavg=1.362, ra=0.0, reward=0.362
Completion 3: length=673, L/Lavg=0.723, ra=0.0, reward=-0.277
Completion 4: length=695, L/Lavg=0.746, ra=0.0, reward=-0.254
Completion 5: length=638, L/Lavg=0.685, ra=0.0, reward=-0.315
Completion 6: length=1139, L/Lavg=1.223, ra=0.0, reward=0.223
Completion 7: length=1164, L/Lavg=1.250, ra=0.0, reward=0.250
------------- 06-00-00-06-828450 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 649.625
Completion 0: length=780, L/Lavg=1.201, ra=0.0, reward=0.201
Completion 1: length=490, L/Lavg=0.754, ra=0.0, reward=-0.246
Completion 2: length=765, L/Lavg=1.178, ra=0.0, reward=0.178
Completion 3: length=589, L/Lavg=0.907, ra=0.0, reward=-0.093
Completion 4: length=701, L/Lavg=1.079, ra=0.0, reward=0.079
Completion 5: length=774, L/Lavg=1.191, ra=0.0, reward=0.191
Completion 6: length=580, L/Lavg=0.893, ra=0.0, reward=-0.107
Completion 7: length=518, L/Lavg=0.797, ra=0.0, reward=-0.203
------------- 06-00-00-06-839021 New Length Reward Debug -------------
Problem Type: ['hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard', 'hard']
Group Accuracy: 0.000
Average Length (Lavg): 1109.25
Completion 0: length=1340, L/Lavg=1.208, ra=0.0, reward=0.208
Completion 1: length=709, L/Lavg=0.639, ra=0.0, reward=-0.361
Completion 2: length=484, L/Lavg=0.436, ra=0.0, reward=-0.564
Completion 3: length=713, L/Lavg=0.643, ra=0.0, reward=-0.357
Completion 4: length=1837, L/Lavg=1.656, ra=0.0, reward=0.656
Completion 5: length=999, L/Lavg=0.901, ra=0.0, reward=-0.099
Completion 6: length=1854, L/Lavg=1.671, ra=0.0, reward=0.671
Completion 7: length=938, L/Lavg=0.846, ra=0.0, reward=-0.154
