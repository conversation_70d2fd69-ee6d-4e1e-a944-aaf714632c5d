Log path set to: ./logs/humanomni_emotion_emer_1format_withpath_withchoice_w5w5_easyhardtag_test.txt
W0809 16:43:53.668885 106060 site-packages/torch/distributed/run.py:792] 
W0809 16:43:53.668885 106060 site-packages/torch/distributed/run.py:792] *****************************************
W0809 16:43:53.668885 106060 site-packages/torch/distributed/run.py:792] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0809 16:43:53.668885 106060 site-packages/torch/distributed/run.py:792] *****************************************
[2025-08-09 16:43:59,190] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-08-09 16:43:59,236] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-08-09 16:43:59,614] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-08-09 16:43:59,625] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-08-09 16:44:01,980] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-08-09 16:44:01,980] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-08-09 16:44:02,073] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-08-09 16:44:02,173] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-08-09 16:44:02,480] [INFO] [comm.py:652:init_distributed] cdb=None
Dataset sorted by difficulty. Distribution: {'easy': 4330, 'hard': 12359}
First 10 samples difficulty order: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
has video in dataset

Map:   0%|          | 0/16689 [00:00<?, ? examples/s]
Map:   8%|▊         | 1338/16689 [00:00<00:01, 13290.75 examples/s]
Map:  17%|█▋        | 2789/16689 [00:00<00:01, 13663.69 examples/s]
Map:  29%|██▊       | 4789/16689 [00:00<00:00, 13390.40 examples/s]
Map:  41%|████      | 6786/16689 [00:00<00:00, 13293.09 examples/s]Dataset sorted by difficulty. Distribution: {'easy': 4330, 'hard': 12359}
First 10 samples difficulty order: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
has video in dataset

Map:   0%|          | 0/16689 [00:00<?, ? examples/s]
Map:  53%|█████▎    | 8786/16689 [00:00<00:00, 13258.85 examples/s]
Map:   8%|▊         | 1343/16689 [00:00<00:01, 13330.41 examples/s]
Map:  17%|█▋        | 2776/16689 [00:00<00:01, 13522.52 examples/s]
Map:  65%|██████▍   | 10786/16689 [00:00<00:00, 13149.87 examples/s]
Map:  29%|██▊       | 4774/16689 [00:00<00:00, 13279.03 examples/s]
Map:  77%|███████▋  | 12785/16689 [00:00<00:00, 13161.36 examples/s]Dataset sorted by difficulty. Distribution: {'easy': 4330, 'hard': 12359}
First 10 samples difficulty order: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
has video in dataset

Map:   0%|          | 0/16689 [00:00<?, ? examples/s]
Map:  40%|████      | 6750/16689 [00:00<00:00, 13062.53 examples/s]
Map:  89%|████████▊ | 14780/16689 [00:01<00:00, 13085.50 examples/s]
Map:   8%|▊         | 1332/16689 [00:00<00:01, 13220.16 examples/s]
Map:  52%|█████▏    | 8755/16689 [00:00<00:00, 13036.77 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 12802.46 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 13050.57 examples/s]
using:  <class 'open_r1.trainer.humanOmni_grpo_trainer.HumanOmniVLGRPOTrainer'>
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-08-09 16:44:05,310] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4

Map:  17%|█▋        | 2785/16689 [00:00<00:01, 13595.75 examples/s]
Map:  64%|██████▍   | 10745/16689 [00:00<00:00, 12918.88 examples/s]Dataset sorted by difficulty. Distribution: {'easy': 4330, 'hard': 12359}
First 10 samples difficulty order: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
has video in dataset

Map:   0%|          | 0/16689 [00:00<?, ? examples/s]
Map:  29%|██▊       | 4764/16689 [00:00<00:00, 13152.01 examples/s]
Map:   7%|▋         | 1115/16689 [00:00<00:01, 11059.33 examples/s]
Map:  76%|███████▋  | 12760/16689 [00:00<00:00, 12872.96 examples/s]
Map:  40%|████      | 6678/16689 [00:00<00:00, 12561.17 examples/s]
Map:  17%|█▋        | 2865/16689 [00:00<00:01, 11471.26 examples/s]
Map:  87%|████████▋ | 14585/16689 [00:01<00:00, 12645.40 examples/s]
Map:  51%|█████     | 8545/16689 [00:00<00:00, 12516.93 examples/s]
Map:  95%|█████████▌| 15920/16689 [00:01<00:00, 12807.01 examples/s]
Map:  59%|█████▉    | 9805/16689 [00:00<00:00, 12538.79 examples/s]
Map:  28%|██▊       | 4664/16689 [00:00<00:01, 11216.47 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 12769.29 examples/s]
using:  <class 'open_r1.trainer.humanOmni_grpo_trainer.HumanOmniVLGRPOTrainer'>
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-08-09 16:44:05,924] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4

Map:  35%|███▍      | 5831/16689 [00:00<00:00, 11354.54 examples/s]
Map:  71%|███████   | 11777/16689 [00:00<00:00, 12681.43 examples/s]
Map:  42%|████▏     | 6979/16689 [00:00<00:00, 11390.21 examples/s]
Map:  52%|█████▏    | 8665/16689 [00:00<00:00, 11310.31 examples/s]
Map:  80%|████████  | 13398/16689 [00:01<00:00, 10796.19 examples/s]
Map:  61%|██████    | 10180/16689 [00:00<00:00, 10869.51 examples/s]
Map:  88%|████████▊ | 14614/16689 [00:01<00:00, 9979.63 examples/s] 
Map:  68%|██████▊   | 11302/16689 [00:01<00:00, 10957.30 examples/s]
Map:  96%|█████████▌| 15965/16689 [00:01<00:00, 9488.82 examples/s]
Map:  75%|███████▍  | 12466/16689 [00:01<00:00, 11138.29 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 10797.27 examples/s]
using:  <class 'open_r1.trainer.humanOmni_grpo_trainer.HumanOmniVLGRPOTrainer'>
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-08-09 16:44:06,671] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4

Map:  82%|████████▏ | 13661/16689 [00:01<00:00, 11257.21 examples/s]
Map:  89%|████████▉ | 14816/16689 [00:01<00:00, 11336.92 examples/s]
Map:  98%|█████████▊| 16425/16689 [00:01<00:00, 11089.06 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 11120.31 examples/s]
using:  <class 'open_r1.trainer.humanOmni_grpo_trainer.HumanOmniVLGRPOTrainer'>
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-08-09 16:44:06,982] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:09,119] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 624, num_elems = 0.78B
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-08-09 16:44:10,692] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:10,692] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:10,693] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-08-09 16:44:10,716] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:10,813] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 832, num_elems = 0.87B
[2025-08-09 16:44:11,126] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:11,126] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:11,127] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:11,137] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:11,434] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 1323, num_elems = 1.51B
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
[2025-08-09 16:44:13,756] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:13,757] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:13,758] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
[2025-08-09 16:44:13,782] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:14,431] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 1947, num_elems = 2.29B
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-08-09 16:44:15,769] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:15,770] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:15,770] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-08-09 16:44:15,794] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:15,889] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 2155, num_elems = 2.38B
[2025-08-09 16:44:16,188] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:16,188] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:16,198] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:16,199] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:16,458] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 2646, num_elems = 3.02B
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.
You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.
You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.
You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.
[2025-08-09 16:44:20,231] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Detected kernel version 3.10.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
[2025-08-09 16:44:20,231] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:20,234] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:20,235] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-08-09 16:44:20,236] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:44:20,260] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-08-09 16:44:20,265] [INFO] [logging.py:128:log_dist] [Rank 0] Creating ZeRO Offload
[2025-08-09 16:44:20,544] [INFO] [utils.py:781:see_memory_usage] DeepSpeedZeRoOffload initialize [begin]
[2025-08-09 16:44:20,545] [INFO] [utils.py:782:see_memory_usage] MA 1.29 GB         Max_MA 1.45 GB         CA 2.05 GB         Max_CA 2 GB 
[2025-08-09 16:44:20,545] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 12.54 GB, percent = 5.0%
Parameter Offload: Total persistent parameters: 967237 in 759 params
[rank1]: Traceback (most recent call last):
[rank1]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
[rank1]:     main(script_args, training_args, model_args)
[rank1]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
[rank1]:     trainer.train()
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank1]:     return inner_training_loop(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2256, in _inner_training_loop
[rank1]:     train_dataloader = self.get_train_dataloader()
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 1053, in get_train_dataloader
[rank1]:     return self._get_dataloader(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 1021, in _get_dataloader
[rank1]:     dataloader_params["sampler"] = sampler_fn(dataset)
[rank1]: TypeError: HumanOmniVLGRPOTrainer._get_train_sampler() takes 1 positional argument but 2 were given
[rank3]: Traceback (most recent call last):
[rank3]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
[rank3]:     main(script_args, training_args, model_args)
[rank3]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
[rank3]:     trainer.train()
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank3]:     return inner_training_loop(
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2256, in _inner_training_loop
[rank3]:     train_dataloader = self.get_train_dataloader()
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 1053, in get_train_dataloader
[rank3]:     return self._get_dataloader(
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 1021, in _get_dataloader
[rank3]:     dataloader_params["sampler"] = sampler_fn(dataset)
[rank3]: TypeError: HumanOmniVLGRPOTrainer._get_train_sampler() takes 1 positional argument but 2 were given
[rank2]: Traceback (most recent call last):
[rank2]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
[rank2]:     main(script_args, training_args, model_args)
[rank2]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
[rank2]:     trainer.train()
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank2]:     return inner_training_loop(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2256, in _inner_training_loop
[rank2]:     train_dataloader = self.get_train_dataloader()
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 1053, in get_train_dataloader
[rank2]:     return self._get_dataloader(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 1021, in _get_dataloader
[rank2]:     dataloader_params["sampler"] = sampler_fn(dataset)
[rank2]: TypeError: HumanOmniVLGRPOTrainer._get_train_sampler() takes 1 positional argument but 2 were given
[2025-08-09 16:44:20,882] [INFO] [utils.py:781:see_memory_usage] DeepSpeedZeRoOffload initialize [end]
[2025-08-09 16:44:20,883] [INFO] [utils.py:782:see_memory_usage] MA 1.29 GB         Max_MA 1.29 GB         CA 2.05 GB         Max_CA 2 GB 
[2025-08-09 16:44:20,883] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 12.54 GB, percent = 5.0%
[2025-08-09 16:44:20,887] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-08-09 16:44:20,887] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false, 
    "contiguous_memory_optimization": false, 
    "cpu_checkpointing": false, 
    "number_checkpoints": null, 
    "synchronize_checkpoint_boundary": false, 
    "profile": false
}
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false, 
    "start_step": null, 
    "end_step": null, 
    "metric_path": null, 
    "arg_mappings": null, 
    "metric": "throughput", 
    "model_info": null, 
    "results_dir": "autotuning_results", 
    "exps_dir": "autotuning_exps", 
    "overwrite": true, 
    "fast": true, 
    "start_profile_step": 3, 
    "end_profile_step": 5, 
    "tuner_type": "gridsearch", 
    "tuner_early_stopping": 5, 
    "tuner_num_trials": 50, 
    "model_info_path": null, 
    "mp_size": 1, 
    "max_train_batch_size": null, 
    "min_train_batch_size": 1, 
    "max_train_micro_batch_size_per_gpu": 1.024000e+03, 
    "min_train_micro_batch_size_per_gpu": 1, 
    "num_tuning_micro_batch_sizes": 3
}
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f00104970a0>
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-08-09 16:44:20,888] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false, 
    "recompute_fwd_factor": 0.0, 
    "profile_step": 1, 
    "module_depth": -1, 
    "top_modules": 1, 
    "detailed": true, 
    "output_file": null
}
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 2
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false, 
    "persistent_storage_path": null, 
    "persistent_time_interval": 100, 
    "num_of_version_in_retention": 2, 
    "enable_nebula_load": true, 
    "load_path": null
}
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-08-09 16:44:20,889] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   train_batch_size ............. 8
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   world_size ................... 4
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  False
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   zero_config .................. stage=3 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=500000000 use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=500000000 overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=DeepSpeedZeroOffloadParamConfig(device='none', nvme_path=None, buffer_count=5, buffer_size=100000000, max_in_cpu=1000000000, pin_memory=True) offload_optimizer=DeepSpeedZeroOffloadOptimizerConfig(device='none', nvme_path=None, buffer_count=4, pin_memory=True, pipeline_read=False, pipeline_write=False, fast_init=False, ratio=1.0) sub_group_size=1000000000 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1000000000 max_reuse_distance=1000000000 gather_16bit_weights_on_model_save=True use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-08-09 16:44:20,890] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 3
[2025-08-09 16:44:20,890] [INFO] [config.py:989:print_user_config]   json = {
    "fp16": {
        "enabled": false, 
        "loss_scale": 0, 
        "loss_scale_window": 1000, 
        "initial_scale_power": 16, 
        "hysteresis": 2, 
        "min_loss_scale": 1
    }, 
    "bf16": {
        "enabled": true
    }, 
    "zero_optimization": {
        "stage": 3, 
        "offload_optimizer": {
            "device": "none", 
            "pin_memory": true
        }, 
        "offload_param": {
            "device": "none", 
            "pin_memory": true
        }, 
        "overlap_comm": true, 
        "contiguous_gradients": true, 
        "sub_group_size": 1.000000e+09, 
        "reduce_bucket_size": "auto", 
        "stage3_prefetch_bucket_size": "auto", 
        "stage3_param_persistence_threshold": "auto", 
        "stage3_max_live_parameters": 1.000000e+09, 
        "stage3_max_reuse_distance": 1.000000e+09, 
        "stage3_gather_16bit_weights_on_model_save": true
    }, 
    "gradient_accumulation_steps": 2, 
    "gradient_clipping": 1.0, 
    "steps_per_print": inf, 
    "train_batch_size": 8, 
    "train_micro_batch_size_per_gpu": 1, 
    "wall_clock_breakdown": false, 
    "zero_optimization.reduce_bucket_size": 8.028160e+05, 
    "zero_optimization.stage3_param_persistence_threshold": 8.960000e+03, 
    "zero_optimization.stage3_prefetch_bucket_size": 7.225344e+05
}
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
[rank0]:     main(script_args, training_args, model_args)
[rank0]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
[rank0]:     trainer.train()
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank0]:     return inner_training_loop(
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2256, in _inner_training_loop
[rank0]:     train_dataloader = self.get_train_dataloader()
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 1053, in get_train_dataloader
[rank0]:     return self._get_dataloader(
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 1021, in _get_dataloader
[rank0]:     dataloader_params["sampler"] = sampler_fn(dataset)
[rank0]: TypeError: HumanOmniVLGRPOTrainer._get_train_sampler() takes 1 positional argument but 2 were given
