Log path set to: ./logs/humanomni_emotion_emer_1format_withpath_withchoice_w5w5_easyhardtag_test.txt
W0809 16:28:31.438204 101678 site-packages/torch/distributed/run.py:792] 
W0809 16:28:31.438204 101678 site-packages/torch/distributed/run.py:792] *****************************************
W0809 16:28:31.438204 101678 site-packages/torch/distributed/run.py:792] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0809 16:28:31.438204 101678 site-packages/torch/distributed/run.py:792] *****************************************
[2025-08-09 16:28:36,710] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-08-09 16:28:36,714] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-08-09 16:28:36,817] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-08-09 16:28:36,927] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-08-09 16:28:39,071] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-08-09 16:28:39,534] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-08-09 16:28:39,619] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-08-09 16:28:39,739] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-08-09 16:28:39,740] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
Dataset sorted by difficulty. Distribution: {'easy': 4330, 'hard': 12359}
First 10 samples difficulty order: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
has video in dataset

Map:   0%|          | 0/16689 [00:00<?, ? examples/s]
Map:   8%|▊         | 1334/16689 [00:00<00:01, 13242.80 examples/s]
Map:  17%|█▋        | 2772/16689 [00:00<00:01, 13518.53 examples/s]
Map:  29%|██▊       | 4774/16689 [00:00<00:00, 13235.52 examples/s]Dataset sorted by difficulty. Distribution: {'easy': 4330, 'hard': 12359}
First 10 samples difficulty order: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
has video in dataset

Map:   0%|          | 0/16689 [00:00<?, ? examples/s]
Map:  41%|████      | 6768/16689 [00:00<00:00, 13121.12 examples/s]Dataset sorted by difficulty. Distribution: {'easy': 4330, 'hard': 12359}
First 10 samples difficulty order: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
has video in dataset

Map:   0%|          | 0/16689 [00:00<?, ? examples/s]
Map:   8%|▊         | 1297/16689 [00:00<00:01, 12872.42 examples/s]
Map:  53%|█████▎    | 8773/16689 [00:00<00:00, 13063.38 examples/s]
Map:   8%|▊         | 1340/16689 [00:00<00:01, 13315.12 examples/s]
Map:  17%|█▋        | 2773/16689 [00:00<00:01, 13379.40 examples/s]
Map:  17%|█▋        | 2787/16689 [00:00<00:01, 13613.24 examples/s]
Map:  65%|██████▍   | 10769/16689 [00:00<00:00, 12922.23 examples/s]
Map:  29%|██▊       | 4760/16689 [00:00<00:00, 13144.25 examples/s]
Map:  29%|██▊       | 4781/16689 [00:00<00:00, 13326.71 examples/s]
Map:  77%|███████▋  | 12772/16689 [00:00<00:00, 12934.48 examples/s]
Map:  41%|████      | 6774/16689 [00:00<00:00, 13029.31 examples/s]Dataset sorted by difficulty. Distribution: {'easy': 4330, 'hard': 12359}
First 10 samples difficulty order: ['easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy', 'easy']
has video in dataset

Map:   0%|          | 0/16689 [00:00<?, ? examples/s]
Map:  41%|████      | 6787/16689 [00:00<00:00, 13096.82 examples/s]
Map:  89%|████████▊ | 14771/16689 [00:01<00:00, 12928.34 examples/s]
Map:   8%|▊         | 1325/16689 [00:00<00:01, 13165.29 examples/s]
Map:  53%|█████▎    | 8773/16689 [00:00<00:00, 13005.44 examples/s]
Map:  17%|█▋        | 2778/16689 [00:00<00:01, 13570.06 examples/s]
Map:  53%|█████▎    | 8781/16689 [00:00<00:00, 13096.64 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 12580.32 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 12853.45 examples/s]
using:  <class 'open_r1.trainer.humanOmni_grpo_trainer.HumanOmniVLGRPOTrainer'>
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-08-09 16:28:42,714] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4

Map:  64%|██████▍   | 10761/16689 [00:00<00:00, 12935.19 examples/s]
Map:  29%|██▊       | 4768/16689 [00:00<00:00, 13240.41 examples/s]
Map:  65%|██████▍   | 10775/16689 [00:00<00:00, 12967.71 examples/s]
Map:  77%|███████▋  | 12774/16689 [00:00<00:00, 12909.87 examples/s]
Map:  41%|████      | 6774/16689 [00:00<00:00, 13136.19 examples/s]
Map:  77%|███████▋  | 12778/16689 [00:00<00:00, 12914.98 examples/s]
Map:  88%|████████▊ | 14768/16689 [00:01<00:00, 12901.74 examples/s]
Map:  53%|█████▎    | 8778/16689 [00:00<00:00, 13099.73 examples/s]
Map:  89%|████████▊ | 14797/16689 [00:01<00:00, 13085.74 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 12635.16 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 12837.69 examples/s]
using:  <class 'open_r1.trainer.humanOmni_grpo_trainer.HumanOmniVLGRPOTrainer'>
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-08-09 16:28:43,221] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4

Map:  65%|██████▍   | 10765/16689 [00:00<00:00, 12986.74 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 12930.15 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 13020.41 examples/s]
using:  <class 'open_r1.trainer.humanOmni_grpo_trainer.HumanOmniVLGRPOTrainer'>
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-08-09 16:28:43,297] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4

Map:  77%|███████▋  | 12770/16689 [00:00<00:00, 12833.45 examples/s]
Map:  88%|████████▊ | 14689/16689 [00:01<00:00, 12661.57 examples/s]
Map:  96%|█████████▌| 16000/16689 [00:01<00:00, 12447.28 examples/s]
Map: 100%|██████████| 16689/16689 [00:01<00:00, 12786.65 examples/s]
using:  <class 'open_r1.trainer.humanOmni_grpo_trainer.HumanOmniVLGRPOTrainer'>
The argument `trust_remote_code` is to be used with Auto classes. It has no effect here and is ignored.
[2025-08-09 16:28:43,766] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:45,905] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 624, num_elems = 0.78B
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-08-09 16:28:47,515] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:47,516] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:47,519] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-08-09 16:28:47,545] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:47,647] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 832, num_elems = 0.87B
[2025-08-09 16:28:47,988] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:47,988] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:47,990] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:48,000] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:48,374] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 1323, num_elems = 1.51B
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
[2025-08-09 16:28:51,075] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:51,075] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:51,076] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
[2025-08-09 16:28:51,109] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:51,794] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 1947, num_elems = 2.29B
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-08-09 16:28:53,599] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:53,599] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:53,599] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Some weights of the model checkpoint at /data/wuyang/PLM/EMER-SFT-0.5B were not used when initializing HumanOmniQwen2ForCausalLM: ['model.audio_tower.audio_tower.classifier.bias', 'model.audio_tower.audio_tower.classifier.weight', 'model.audio_tower.audio_tower.encoder.conv1.bias', 'model.audio_tower.audio_tower.encoder.conv1.weight', 'model.audio_tower.audio_tower.encoder.conv2.bias', 'model.audio_tower.audio_tower.encoder.conv2.weight', 'model.audio_tower.audio_tower.encoder.embed_positions.weight', 'model.audio_tower.audio_tower.encoder.layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.0.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.0.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.1.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.1.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.10.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.10.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.11.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.11.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.12.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.12.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.13.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.13.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.14.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.14.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.15.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.15.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.16.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.16.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.17.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.17.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.18.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.18.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.19.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.19.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.2.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.2.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.20.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.20.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.21.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.21.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.22.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.22.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.23.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.23.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.24.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.24.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.25.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.25.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.26.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.26.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.27.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.27.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.28.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.28.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.29.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.29.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.3.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.3.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.30.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.30.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.31.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.31.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.4.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.4.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.5.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.5.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.6.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.6.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.7.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.7.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.8.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.8.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc1.weight', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.bias', 'model.audio_tower.audio_tower.encoder.layers.9.fc2.weight', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.final_layer_norm.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.k_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.out_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.q_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn.v_proj.weight', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.bias', 'model.audio_tower.audio_tower.encoder.layers.9.self_attn_layer_norm.weight', 'model.audio_tower.audio_tower.projector.bias', 'model.audio_tower.audio_tower.projector.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.bias', 'model.vision_tower.vision_tower.vision_model.embeddings.patch_embedding.weight', 'model.vision_tower.vision_tower.vision_model.embeddings.position_embedding.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.0.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.1.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.10.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.11.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.2.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.3.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.4.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.5.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.6.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.7.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.8.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.layer_norm2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.k_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.q_proj.weight', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.bias', 'model.vision_tower.vision_tower.vision_model.encoder.layers.9.self_attn.v_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_bias', 'model.vision_tower.vision_tower.vision_model.head.attention.in_proj_weight', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.bias', 'model.vision_tower.vision_tower.vision_model.head.attention.out_proj.weight', 'model.vision_tower.vision_tower.vision_model.head.layernorm.bias', 'model.vision_tower.vision_tower.vision_model.head.layernorm.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc1.weight', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.bias', 'model.vision_tower.vision_tower.vision_model.head.mlp.fc2.weight', 'model.vision_tower.vision_tower.vision_model.head.probe', 'model.vision_tower.vision_tower.vision_model.post_layernorm.bias', 'model.vision_tower.vision_tower.vision_model.post_layernorm.weight']
- This IS expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model trained on another task or with another architecture (e.g. initializing a BertForSequenceClassification model from a BertForPreTraining model).
- This IS NOT expected if you are initializing HumanOmniQwen2ForCausalLM from the checkpoint of a model that you expect to be exactly identical (initializing a BertForSequenceClassification model from a BertForSequenceClassification model).
[2025-08-09 16:28:53,639] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:53,758] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 2155, num_elems = 2.38B
[2025-08-09 16:28:54,070] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:54,070] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:54,070] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:54,082] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:54,321] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 2646, num_elems = 3.02B
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of WhisperForAudioClassification were not initialized from the model checkpoint at /data/wuyang/PLM/whisper-large-v3 and are newly initialized: ['classifier.bias', 'classifier.weight', 'projector.bias', 'projector.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
Using a slow image processor as `use_fast` is unset and a slow processor was saved with this model. `use_fast=True` will be the default behavior in v4.52, even if the model was saved with a slow processor. This will result in minor differences in outputs. You'll still be able to use a slow processor with `use_fast=False`.
You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.
You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.
You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.
You have video processor config saved in `preprocessor.json` file which is deprecated. Video processor configs should be saved in their own `video_preprocessor.json` file. You can rename the file or load and save the processor back which renames it automatically. Loading from `preprocessor.json` will be removed in v5.0.
[2025-08-09 16:28:57,765] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
Detected kernel version 3.10.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.
[2025-08-09 16:28:57,803] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-08-09 16:28:57,803] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:57,810] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:57,812] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
[2025-08-09 16:28:57,827] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-08-09 16:28:57,832] [INFO] [logging.py:128:log_dist] [Rank 0] Creating ZeRO Offload
[2025-08-09 16:28:58,134] [INFO] [utils.py:781:see_memory_usage] DeepSpeedZeRoOffload initialize [begin]
[2025-08-09 16:28:58,135] [INFO] [utils.py:782:see_memory_usage] MA 1.29 GB         Max_MA 1.45 GB         CA 1.79 GB         Max_CA 2 GB 
[2025-08-09 16:28:58,135] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 12.62 GB, percent = 5.0%
Parameter Offload: Total persistent parameters: 967237 in 759 params
[2025-08-09 16:28:58,466] [INFO] [utils.py:781:see_memory_usage] DeepSpeedZeRoOffload initialize [end]
[2025-08-09 16:28:58,467] [INFO] [utils.py:782:see_memory_usage] MA 1.29 GB         Max_MA 1.29 GB         CA 1.79 GB         Max_CA 2 GB 
[2025-08-09 16:28:58,467] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 12.64 GB, percent = 5.0%
[2025-08-09 16:28:58,471] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-08-09 16:28:58,471] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false, 
    "contiguous_memory_optimization": false, 
    "cpu_checkpointing": false, 
    "number_checkpoints": null, 
    "synchronize_checkpoint_boundary": false, 
    "profile": false
}
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false, 
    "start_step": null, 
    "end_step": null, 
    "metric_path": null, 
    "arg_mappings": null, 
    "metric": "throughput", 
    "model_info": null, 
    "results_dir": "autotuning_results", 
    "exps_dir": "autotuning_exps", 
    "overwrite": true, 
    "fast": true, 
    "start_profile_step": 3, 
    "end_profile_step": 5, 
    "tuner_type": "gridsearch", 
    "tuner_early_stopping": 5, 
    "tuner_num_trials": 50, 
    "model_info_path": null, 
    "mp_size": 1, 
    "max_train_batch_size": null, 
    "min_train_batch_size": 1, 
    "max_train_micro_batch_size_per_gpu": 1.024000e+03, 
    "min_train_micro_batch_size_per_gpu": 1, 
    "num_tuning_micro_batch_sizes": 3
}
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f3f981e80a0>
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-08-09 16:28:58,472] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false, 
    "recompute_fwd_factor": 0.0, 
    "profile_step": 1, 
    "module_depth": -1, 
    "top_modules": 1, 
    "detailed": true, 
    "output_file": null
}
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 2
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false, 
    "persistent_storage_path": null, 
    "persistent_time_interval": 100, 
    "num_of_version_in_retention": 2, 
    "enable_nebula_load": true, 
    "load_path": null
}
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-08-09 16:28:58,473] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   train_batch_size ............. 8
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   world_size ................... 4
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  False
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   zero_config .................. stage=3 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=500000000 use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=500000000 overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=DeepSpeedZeroOffloadParamConfig(device='none', nvme_path=None, buffer_count=5, buffer_size=100000000, max_in_cpu=1000000000, pin_memory=True) offload_optimizer=DeepSpeedZeroOffloadOptimizerConfig(device='none', nvme_path=None, buffer_count=4, pin_memory=True, pipeline_read=False, pipeline_write=False, fast_init=False, ratio=1.0) sub_group_size=1000000000 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1000000000 max_reuse_distance=1000000000 gather_16bit_weights_on_model_save=True use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-08-09 16:28:58,474] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 3
[2025-08-09 16:28:58,474] [INFO] [config.py:989:print_user_config]   json = {
    "fp16": {
        "enabled": false, 
        "loss_scale": 0, 
        "loss_scale_window": 1000, 
        "initial_scale_power": 16, 
        "hysteresis": 2, 
        "min_loss_scale": 1
    }, 
    "bf16": {
        "enabled": true
    }, 
    "zero_optimization": {
        "stage": 3, 
        "offload_optimizer": {
            "device": "none", 
            "pin_memory": true
        }, 
        "offload_param": {
            "device": "none", 
            "pin_memory": true
        }, 
        "overlap_comm": true, 
        "contiguous_gradients": true, 
        "sub_group_size": 1.000000e+09, 
        "reduce_bucket_size": "auto", 
        "stage3_prefetch_bucket_size": "auto", 
        "stage3_param_persistence_threshold": "auto", 
        "stage3_max_live_parameters": 1.000000e+09, 
        "stage3_max_reuse_distance": 1.000000e+09, 
        "stage3_gather_16bit_weights_on_model_save": true
    }, 
    "gradient_accumulation_steps": 2, 
    "gradient_clipping": 1.0, 
    "steps_per_print": inf, 
    "train_batch_size": 8, 
    "train_micro_batch_size_per_gpu": 1, 
    "wall_clock_breakdown": false, 
    "zero_optimization.reduce_bucket_size": 8.028160e+05, 
    "zero_optimization.stage3_param_persistence_threshold": 8.960000e+03, 
    "zero_optimization.stage3_prefetch_bucket_size": 7.225344e+05
}
Gradient accumulation steps mismatch: GradientAccumulationPlugin has 1, DeepSpeed config has 2. Using DeepSpeed's value.
Parameter Offload: Total persistent parameters: 956485 in 758 params
wandb: Tracking run with wandb version 0.21.0
wandb: W&B syncing is set to `offline` in this directory. Run `wandb online` or set WANDB_MODE=online to enable cloud syncing.

  0%|          | 0/4174 [00:00<?, ?it/s]Invalidate trace cache @ step 0 and module 2618: cache has only 0 modules
Invalidate trace cache @ step 0 and module 3927: cache has only 0 modules

  0%|          | 1/4174 [00:39<45:56:15, 39.63s/it]
                                                   
{'loss': 0.0001, 'grad_norm': 4.900435865418909, 'learning_rate': 1e-06, 'completion_length': 175.859375, 'rewards/format_reward': 1.0, 'rewards/direct_length_reward': -0.24421483278274536, 'reward': 0.7557851076126099, 'reward_std': 0.4590848535299301, 'kl': 0.001373291015625, 'epoch': 0.0}

  0%|          | 1/4174 [00:39<45:56:15, 39.63s/it]Invalidate trace cache @ step 0 and module 5236: cache has only 0 modules
Invalidate trace cache @ step 0 and module 6545: cache has only 0 modules

  0%|          | 2/4174 [01:11<40:36:42, 35.04s/it]
                                                   
{'loss': 0.0001, 'grad_norm': 4.65818892138507, 'learning_rate': 9.99760421657882e-07, 'completion_length': 170.0625, 'rewards/format_reward': 1.0, 'rewards/direct_length_reward': -0.22494103014469147, 'reward': 0.7750589549541473, 'reward_std': 0.48211362957954407, 'kl': 0.001430511474609375, 'epoch': 0.0}

  0%|          | 2/4174 [01:11<40:36:42, 35.04s/it]Invalidate trace cache @ step 0 and module 7854: cache has only 0 modules
Invalidate trace cache @ step 0 and module 9163: cache has only 0 modules

  0%|          | 3/4174 [01:51<43:22:00, 37.43s/it]
                                                   
{'loss': 0.0001, 'grad_norm': 4.762560898306155, 'learning_rate': 9.995208433157642e-07, 'completion_length': 170.640625, 'rewards/format_reward': 0.984375, 'rewards/direct_length_reward': -0.13895156094804406, 'reward': 0.8454234302043915, 'reward_std': 0.40086521208286285, 'kl': 0.0012969970703125, 'epoch': 0.0}

  0%|          | 3/4174 [01:51<43:22:00, 37.43s/it]W0809 16:31:08.450801 101678 site-packages/torch/distributed/elastic/agent/server/api.py:719] Received Signals.SIGINT death signal, shutting down workers
W0809 16:31:08.452471 101678 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 101756 closing signal SIGINT
W0809 16:31:08.453090 101678 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 101757 closing signal SIGINT
W0809 16:31:08.453624 101678 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 101758 closing signal SIGINT
W0809 16:31:08.454162 101678 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 101759 closing signal SIGINT
[rank1]: Traceback (most recent call last):
[rank1]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
[rank1]:     main(script_args, training_args, model_args)
[rank1]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
[rank1]:     trainer.train()
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank1]:     return inner_training_loop(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2548, in _inner_training_loop
[rank1]:     tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 3749, in training_step
[rank1]:     loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
[rank1]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/trainer/humanOmni_grpo_trainer.py", line 492, in compute_loss
[rank1]:     prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=self.generation_config)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank1]:     return func(*args, **kwargs)
[rank1]:   File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 171, in generate
[rank1]:     return super().generate(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank1]:     return func(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 2625, in generate
[rank1]:     result = self._sample(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 3609, in _sample
[rank1]:     outputs = model_forward(**model_inputs, return_dict=True)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1845, in _call_impl
[rank1]:     return inner()
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1793, in inner
[rank1]:     result = forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 98, in forward
[rank1]:     outputs = super().forward(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank1]:     output = func(self, *args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 544, in forward
[rank1]:     outputs: BaseModelOutputWithPast = self.model(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank1]:     output = func(self, *args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 432, in forward
[rank1]:     layer_outputs = decoder_layer(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/modeling_layers.py", line 83, in __call__
[rank1]:     return super().__call__(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 236, in forward
[rank1]:     hidden_states, self_attn_weights = self.self_attn(
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 183, in forward
[rank1]:     attn_output = self.o_proj(attn_output)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank1]:     return self._call_impl(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank1]:     return forward_call(*args, **kwargs)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/linear.py", line 125, in forward
[rank1]:     return F.linear(input, self.weight, self.bias)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/deepspeed/runtime/zero/linear.py", line 116, in zero3_linear_wrap
[rank1]:     return LinearFunctionForZeroStage3.apply(input, weight)
[rank1]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/autograd/function.py", line 575, in apply
[rank1]:     return super().apply(*args, **kwargs)  # type: ignore[misc]
[rank1]: KeyboardInterrupt
Traceback (most recent call last):
  File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
    main(script_args, training_args, model_args)
  File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
    trainer.train()
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
    return inner_training_loop(
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2548, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 3749, in training_step
    loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
  File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/trainer/humanOmni_grpo_trainer.py", line 492, in compute_loss
    prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=self.generation_config)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
    return func(*args, **kwargs)
  File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 171, in generate
    return super().generate(
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
    return func(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 2625, in generate
    result = self._sample(
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 3609, in _sample
    outputs = model_forward(**model_inputs, return_dict=True)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1845, in _call_impl
    return inner()
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1793, in inner
    result = forward_call(*args, **kwargs)
  File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 98, in forward
    outputs = super().forward(
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
    output = func(self, *args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 544, in forward
    outputs: BaseModelOutputWithPast = self.model(
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
    output = func(self, *args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 432, in forward
    layer_outputs = decoder_layer(
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/modeling_layers.py", line 83, in __call__
    return super().__call__(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
[rank3]: Traceback (most recent call last):
[rank3]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
[rank3]:     main(script_args, training_args, model_args)
[rank3]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
[rank3]:     trainer.train()
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank3]:     return inner_training_loop(
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2548, in _inner_training_loop
[rank3]:     tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 3749, in training_step
[rank3]:     loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
[rank3]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/trainer/humanOmni_grpo_trainer.py", line 492, in compute_loss
[rank3]:     prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=self.generation_config)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank3]:     return func(*args, **kwargs)
[rank3]:   File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 171, in generate
[rank3]:     return super().generate(
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank3]:     return func(*args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 2625, in generate
[rank3]:     result = self._sample(
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 3609, in _sample
[rank3]:     outputs = model_forward(**model_inputs, return_dict=True)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank3]:     return self._call_impl(*args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1845, in _call_impl
[rank3]:     return inner()
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1793, in inner
[rank3]:     result = forward_call(*args, **kwargs)
[rank3]:   File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 98, in forward
[rank3]:     outputs = super().forward(
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank3]:     output = func(self, *args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 544, in forward
[rank3]:     outputs: BaseModelOutputWithPast = self.model(
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank3]:     return self._call_impl(*args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank3]:     return forward_call(*args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank3]:     output = func(self, *args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 432, in forward
[rank3]:     layer_outputs = decoder_layer(
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/modeling_layers.py", line 83, in __call__
[rank3]:     return super().__call__(*args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank3]:     return self._call_impl(*args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank3]:     return forward_call(*args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 233, in forward
[rank3]:     hidden_states = self.input_layernorm(hidden_states)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank3]:     return self._call_impl(*args, **kwargs)
[rank3]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank3]:     return forward_call(*args, **kwargs)
[rank3]: KeyboardInterrupt
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 233, in forward
    hidden_states = self.input_layernorm(hidden_states)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
    return forward_call(*args, **kwargs)
KeyboardInterrupt
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
[rank0]:     main(script_args, training_args, model_args)
[rank0]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
[rank0]:     trainer.train()
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank0]:     return inner_training_loop(
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2548, in _inner_training_loop
[rank0]:     tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 3749, in training_step
[rank0]:     loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
[rank0]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/trainer/humanOmni_grpo_trainer.py", line 492, in compute_loss
[rank0]:     prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=self.generation_config)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank0]:     return func(*args, **kwargs)
[rank0]:   File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 171, in generate
[rank0]:     return super().generate(
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank0]:     return func(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 2625, in generate
[rank0]:     result = self._sample(
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 3609, in _sample
[rank0]:     outputs = model_forward(**model_inputs, return_dict=True)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1845, in _call_impl
[rank0]:     return inner()
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1793, in inner
[rank0]:     result = forward_call(*args, **kwargs)
[rank0]:   File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 98, in forward
[rank0]:     outputs = super().forward(
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank0]:     output = func(self, *args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 544, in forward
[rank0]:     outputs: BaseModelOutputWithPast = self.model(
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank0]:     output = func(self, *args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 432, in forward
[rank0]:     layer_outputs = decoder_layer(
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/modeling_layers.py", line 83, in __call__
[rank0]:     return super().__call__(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 233, in forward
[rank0]:     hidden_states = self.input_layernorm(hidden_states)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank0]:     return self._call_impl(*args, **kwargs)
[rank0]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank0]:     return forward_call(*args, **kwargs)
[rank0]: KeyboardInterrupt
[rank2]: Traceback (most recent call last):
[rank2]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 542, in <module>
[rank2]:     main(script_args, training_args, model_args)
[rank2]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/grpo.py", line 531, in main
[rank2]:     trainer.train()
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2206, in train
[rank2]:     return inner_training_loop(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 2548, in _inner_training_loop
[rank2]:     tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/trainer.py", line 3749, in training_step
[rank2]:     loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
[rank2]:   File "/data/wuyang/R1-Omni-main/src/r1-v/src/open_r1/trainer/humanOmni_grpo_trainer.py", line 492, in compute_loss
[rank2]:     prompt_completion_ids = unwrapped_model.generate(**prompt_inputs, generation_config=self.generation_config)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank2]:     return func(*args, **kwargs)
[rank2]:   File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 171, in generate
[rank2]:     return super().generate(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/utils/_contextlib.py", line 116, in decorate_context
[rank2]:     return func(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 2625, in generate
[rank2]:     result = self._sample(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/generation/utils.py", line 3609, in _sample
[rank2]:     outputs = model_forward(**model_inputs, return_dict=True)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank2]:     return self._call_impl(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1845, in _call_impl
[rank2]:     return inner()
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1793, in inner
[rank2]:     result = forward_call(*args, **kwargs)
[rank2]:   File "/data/wuyang/R1-Omni-main/humanomni/model/humanomni_model.py", line 98, in forward
[rank2]:     outputs = super().forward(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank2]:     output = func(self, *args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 544, in forward
[rank2]:     outputs: BaseModelOutputWithPast = self.model(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank2]:     return self._call_impl(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank2]:     return forward_call(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/utils/generic.py", line 943, in wrapper
[rank2]:     output = func(self, *args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 432, in forward
[rank2]:     layer_outputs = decoder_layer(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/modeling_layers.py", line 83, in __call__
[rank2]:     return super().__call__(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank2]:     return self._call_impl(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank2]:     return forward_call(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 236, in forward
[rank2]:     hidden_states, self_attn_weights = self.self_attn(
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1739, in _wrapped_call_impl
[rank2]:     return self._call_impl(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1750, in _call_impl
[rank2]:     return forward_call(*args, **kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/models/qwen2/modeling_qwen2.py", line 164, in forward
[rank2]:     key_states, value_states = past_key_value.update(key_states, value_states, self.layer_idx, cache_kwargs)
[rank2]:   File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/transformers/cache_utils.py", line 558, in update
[rank2]:     self.value_cache[layer_idx] = torch.cat([self.value_cache[layer_idx], value_states], dim=-2)
[rank2]: KeyboardInterrupt
W0809 16:31:09.041319 101678 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 101756 closing signal SIGTERM
W0809 16:31:09.043254 101678 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 101757 closing signal SIGTERM
W0809 16:31:09.045015 101678 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 101758 closing signal SIGTERM
W0809 16:31:09.046420 101678 site-packages/torch/distributed/elastic/multiprocessing/api.py:897] Sending process 101759 closing signal SIGTERM
Traceback (most recent call last):
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 711, in run
    result = self._invoke_run(role)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 870, in _invoke_run
    time.sleep(monitor_interval)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 84, in _terminate_process_handler
    raise SignalException(f"Process {os.getpid()} got signal: {sigval}", sigval=sigval)
torch.distributed.elastic.multiprocessing.api.SignalException: Process 101678 got signal: 2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 720, in run
    self._shutdown(e.sigval)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/local_elastic_agent.py", line 372, in _shutdown
    self._pcontext.close(death_sig)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 572, in close
    self._close(death_sig=death_sig, timeout=timeout)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 909, in _close
    handler.proc.wait(time_to_wait)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/subprocess.py", line 1207, in wait
    return self._wait(timeout=timeout)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/subprocess.py", line 1935, in _wait
    time.sleep(delay)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 84, in _terminate_process_handler
    raise SignalException(f"Process {os.getpid()} got signal: {sigval}", sigval=sigval)
torch.distributed.elastic.multiprocessing.api.SignalException: Process 101678 got signal: 2

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/data/wuyang/conda_envs/mm-upt/bin/torchrun", line 8, in <module>
    sys.exit(main())
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/errors/__init__.py", line 355, in wrapper
    return f(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/run.py", line 918, in main
    run(args)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/run.py", line 909, in run
    elastic_launch(
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 138, in __call__
    return launch_agent(self._config, self._entrypoint, list(args))
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/launcher/api.py", line 260, in launch_agent
    result = agent.run()
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/metrics/api.py", line 137, in wrapper
    result = f(*args, **kwargs)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/api.py", line 725, in run
    self._shutdown()
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/agent/server/local_elastic_agent.py", line 372, in _shutdown
    self._pcontext.close(death_sig)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 572, in close
    self._close(death_sig=death_sig, timeout=timeout)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 909, in _close
    handler.proc.wait(time_to_wait)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/subprocess.py", line 1207, in wait
    return self._wait(timeout=timeout)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/subprocess.py", line 1935, in _wait
    time.sleep(delay)
  File "/data/wuyang/conda_envs/mm-upt/lib/python3.10/site-packages/torch/distributed/elastic/multiprocessing/api.py", line 84, in _terminate_process_handler
    raise SignalException(f"Process {os.getpid()} got signal: {sigval}", sigval=sigval)
torch.distributed.elastic.multiprocessing.api.SignalException: Process 101678 got signal: 2
