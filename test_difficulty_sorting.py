#!/usr/bin/env python3
"""
测试脚本：验证数据集按difficulty排序功能
"""
import json
import sys
import os

def get_difficulty_order(difficulty):
    """排序函数：easy=0, hard=1, 其他=2"""
    if difficulty == "easy":
        return 0
    elif difficulty == "hard":
        return 1
    else:
        return 2

def create_test_dataset():
    """创建一个测试数据集文件"""
    test_data = [
        {
            "video": "/path/to/video1.mp4",
            "difficulty": "hard",
            "conversations": [
                {"from": "human", "value": "What emotion is shown?"},
                {"from": "gpt", "value": "The person shows happiness."}
            ]
        },
        {
            "video": "/path/to/video2.mp4", 
            "difficulty": "easy",
            "conversations": [
                {"from": "human", "value": "What emotion is shown?"},
                {"from": "gpt", "value": "The person shows sadness."}
            ]
        },
        {
            "video": "/path/to/video3.mp4",
            "difficulty": "hard", 
            "conversations": [
                {"from": "human", "value": "What emotion is shown?"},
                {"from": "gpt", "value": "The person shows anger."}
            ]
        },
        {
            "video": "/path/to/video4.mp4",
            "difficulty": "easy",
            "conversations": [
                {"from": "human", "value": "What emotion is shown?"},
                {"from": "gpt", "value": "The person shows surprise."}
            ]
        },
        {
            "video": "/path/to/video5.mp4",
            "difficulty": "unknown",
            "conversations": [
                {"from": "human", "value": "What emotion is shown?"},
                {"from": "gpt", "value": "The person shows fear."}
            ]
        }
    ]
    
    test_file = "test_dataset.json"
    with open(test_file, 'w') as f:
        json.dump(test_data, f, indent=2)
    
    return test_file

def test_difficulty_sorting():
    """测试difficulty排序功能"""
    print("Testing difficulty sorting logic...")

    # 创建测试数据
    test_data = [
        {"video": "/path/to/video1.mp4", "difficulty": "hard"},
        {"video": "/path/to/video2.mp4", "difficulty": "easy"},
        {"video": "/path/to/video3.mp4", "difficulty": "hard"},
        {"video": "/path/to/video4.mp4", "difficulty": "easy"},
        {"video": "/path/to/video5.mp4", "difficulty": "unknown"}
    ]

    print("Original order:")
    for i, item in enumerate(test_data):
        print(f"  {i}: {item['video']} - {item['difficulty']}")

    # 应用排序逻辑
    sort_indices = sorted(range(len(test_data)), key=lambda i: get_difficulty_order(test_data[i]['difficulty']))
    sorted_data = [test_data[i] for i in sort_indices]

    print("\nSorted order:")
    for i, item in enumerate(sorted_data):
        print(f"  {i}: {item['video']} - {item['difficulty']}")

    # 验证排序结果
    difficulties = [item['difficulty'] for item in sorted_data]
    expected_order = ["easy", "easy", "hard", "hard", "unknown"]

    if difficulties == expected_order:
        print("\n✅ Sorting test PASSED! Easy samples come first, then hard, then others.")
        return True
    else:
        print(f"\n❌ Sorting test FAILED! Expected: {expected_order}, Got: {difficulties}")
        return False

if __name__ == "__main__":
    test_difficulty_sorting()
