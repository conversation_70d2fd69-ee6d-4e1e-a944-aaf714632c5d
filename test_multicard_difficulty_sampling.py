#!/usr/bin/env python3
"""
测试脚本：验证多卡训练时difficulty采样顺序
"""
import torch
import torch.distributed as dist
from torch.utils.data import Dataset

# 模拟DifficultyOrderedSampler
class DifficultyOrderedSampler:
    """
    自定义采样器，保持difficulty的顺序：easy -> hard -> others
    在多卡训练时，每个GPU按顺序获得连续的数据块，而不是随机分布
    """
    
    def __init__(self, dataset, num_replicas=None, rank=None, num_generations=1):
        self.dataset = dataset
        self.num_replicas = num_replicas if num_replicas is not None else 1
        self.rank = rank if rank is not None else 0
        self.num_generations = num_generations
        
        # 计算每个GPU应该处理的数据量
        self.num_samples = len(dataset) // self.num_replicas
        if len(dataset) % self.num_replicas != 0:
            # 如果不能整除，前面的GPU多处理一个样本
            if self.rank < len(dataset) % self.num_replicas:
                self.num_samples += 1
        
        # 计算当前GPU的数据范围
        samples_per_rank = [len(dataset) // self.num_replicas] * self.num_replicas
        for i in range(len(dataset) % self.num_replicas):
            samples_per_rank[i] += 1
            
        self.start_idx = sum(samples_per_rank[:self.rank])
        self.end_idx = self.start_idx + self.num_samples
        
        print(f"Rank {self.rank}: processing samples {self.start_idx} to {self.end_idx-1} (total: {self.num_samples})")
        
    def __iter__(self):
        # 为GRPO重复每个索引num_generations次
        indices = []
        for idx in range(self.start_idx, self.end_idx):
            indices.extend([idx] * self.num_generations)
        return iter(indices)
    
    def __len__(self):
        return self.num_samples * self.num_generations


class MockDataset(Dataset):
    """模拟数据集"""
    def __init__(self, difficulties):
        self.difficulties = difficulties
    
    def __len__(self):
        return len(self.difficulties)
    
    def __getitem__(self, idx):
        return {"difficulty": self.difficulties[idx], "idx": idx}


def test_multicard_sampling():
    """测试多卡采样"""
    print("Testing multi-card difficulty sampling...")
    
    # 创建已排序的数据集：easy -> hard -> unknown
    difficulties = (
        ["easy"] * 6 +      # 6个easy样本
        ["hard"] * 8 +      # 8个hard样本  
        ["unknown"] * 2     # 2个unknown样本
    )
    
    print(f"Total dataset: {len(difficulties)} samples")
    print(f"Distribution: {difficulties}")
    
    dataset = MockDataset(difficulties)
    
    # 模拟4卡训练
    num_gpus = 4
    num_generations = 2  # GRPO的生成数
    
    print(f"\nSimulating {num_gpus}-GPU training with {num_generations} generations per sample:")
    
    all_samples = {}
    
    for rank in range(num_gpus):
        print(f"\n--- GPU {rank} ---")
        sampler = DifficultyOrderedSampler(
            dataset=dataset,
            num_replicas=num_gpus,
            rank=rank,
            num_generations=num_generations
        )
        
        # 获取该GPU的采样索引
        indices = list(sampler)
        samples = [dataset[idx] for idx in indices]
        
        print(f"Sampled indices: {indices}")
        print(f"Difficulties: {[s['difficulty'] for s in samples]}")
        
        # 检查是否保持了顺序
        difficulties_on_gpu = [s['difficulty'] for s in samples]
        unique_difficulties = []
        for d in difficulties_on_gpu:
            if not unique_difficulties or unique_difficulties[-1] != d:
                unique_difficulties.append(d)
        
        print(f"Unique difficulty sequence: {unique_difficulties}")
        
        # 验证是否按顺序
        expected_sequences = [
            ["easy"],
            ["easy", "hard"], 
            ["hard"],
            ["hard", "unknown"],
            ["unknown"]
        ]
        
        is_ordered = unique_difficulties in expected_sequences
        print(f"Order preserved: {'✅' if is_ordered else '❌'}")
        
        all_samples[rank] = samples
    
    # 验证所有GPU覆盖了完整数据集
    print(f"\n--- Overall Verification ---")
    all_indices = []
    for rank in range(num_gpus):
        sampler = DifficultyOrderedSampler(dataset, num_gpus, rank, num_generations)
        indices = list(sampler)
        # 去重（因为GRPO会重复）
        unique_indices = []
        for idx in indices:
            if idx not in unique_indices:
                unique_indices.append(idx)
        all_indices.extend(unique_indices)
    
    all_indices.sort()
    expected_indices = list(range(len(dataset)))
    
    print(f"All covered indices: {all_indices}")
    print(f"Expected indices: {expected_indices}")
    print(f"Complete coverage: {'✅' if all_indices == expected_indices else '❌'}")
    
    # 验证每个GPU内部的顺序
    print(f"\n--- Per-GPU Order Check ---")
    overall_order_preserved = True
    
    for rank in range(num_gpus):
        sampler = DifficultyOrderedSampler(dataset, num_gpus, rank, num_generations)
        indices = list(sampler)
        # 去重保持顺序
        seen = set()
        unique_indices = []
        for idx in indices:
            if idx not in seen:
                unique_indices.append(idx)
                seen.add(idx)
        
        # 检查索引是否递增（保持原始顺序）
        is_ascending = all(unique_indices[i] <= unique_indices[i+1] for i in range(len(unique_indices)-1))
        print(f"GPU {rank} indices ascending: {'✅' if is_ascending else '❌'} {unique_indices}")
        
        if not is_ascending:
            overall_order_preserved = False
    
    print(f"\nOverall result: {'✅ SUCCESS' if overall_order_preserved else '❌ FAILED'}")
    print("Easy samples will be processed first across all GPUs, then hard samples.")


if __name__ == "__main__":
    test_multicard_sampling()
